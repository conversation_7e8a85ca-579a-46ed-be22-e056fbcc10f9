<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Learn Japanese - Advanced Learning Platform</title>
  <!-- Offline-capable embedded styles -->
  <style>
    /* Essential Tailwind-like utilities for offline use */
    .flex { display: flex; }
    .flex-col { flex-direction: column; }
    .items-center { align-items: center; }
    .justify-center { justify-content: center; }
    .justify-between { justify-content: space-between; }
    .gap-1 { gap: 0.25rem; }
    .gap-2 { gap: 0.5rem; }
    .gap-3 { gap: 0.75rem; }
    .gap-4 { gap: 1rem; }
    .p-2 { padding: 0.5rem; }
    .p-3 { padding: 0.75rem; }
    .p-4 { padding: 1rem; }
    .px-4 { padding-left: 1rem; padding-right: 1rem; }
    .py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
    .m-2 { margin: 0.5rem; }
    .mb-1 { margin-bottom: 0.25rem; }
    .mb-2 { margin-bottom: 0.5rem; }
    .mb-3 { margin-bottom: 0.75rem; }
    .mb-4 { margin-bottom: 1rem; }
    .w-full { width: 100%; }
    .h-full { height: 100%; }
    .min-h-screen { min-height: 100vh; }
    .fixed { position: fixed; }
    .top-4 { top: 1rem; }
    .right-4 { right: 1rem; }
    .left-4 { left: 1rem; }
    .z-50 { z-index: 50; }
    .rounded-lg { border-radius: 0.5rem; }
    .rounded-xl { border-radius: 0.75rem; }
    .rounded-full { border-radius: 9999px; }
    .text-white { color: white; }
    .text-lg { font-size: 1.125rem; }
    .text-xl { font-size: 1.25rem; }
    .text-2xl { font-size: 1.5rem; }
    .font-bold { font-weight: bold; }
    .bg-red-500 { background-color: #ef4444; }
    .bg-green-500 { background-color: #22c55e; }
    .bg-orange-500 { background-color: #f97316; }
    .bg-blue-500 { background-color: #3b82f6; }
    .bg-opacity-20 { background-color: rgba(255, 255, 255, 0.2); }
    .bg-opacity-50 { background-color: rgba(255, 255, 255, 0.5); }
    .border-t { border-top-width: 1px; }
    .border-white { border-color: white; }
    .border-opacity-20 { border-color: rgba(255, 255, 255, 0.2); }
    .pt-3 { padding-top: 0.75rem; }
    .cursor-pointer { cursor: pointer; }
    .transition-all { transition: all 0.3s ease; }
    .transition-colors { transition: color 0.3s ease; }
    .hover\:bg-white:hover { background-color: white; }
    .hover\:text-white:hover { color: white; }
    .hover\:text-red-300:hover { color: #fca5a5; }
    .text-opacity-60 { color: rgba(255, 255, 255, 0.6); }
    .text-opacity-80 { color: rgba(255, 255, 255, 0.8); }
    .text-green-400 { color: #4ade80; }
    .text-yellow-400 { color: #facc15; }
    .text-red-400 { color: #f87171; }
    .text-sm { font-size: 0.875rem; }
    .hidden { display: none; }
    .block { display: block; }

    /* Font Awesome icon replacements */
    .fas::before { font-family: 'Font Awesome 6 Free'; font-weight: 900; }
    .fa-volume-up::before { content: '🔊'; }
    .fa-volume-mute::before { content: '🔇'; }
    .fa-music::before { content: '🎵'; }
    .fa-play::before { content: '▶️'; }
    .fa-heart::before { content: '❤️'; }
    .fa-wifi::before { content: '📶'; }
    .fa-wifi-slash::before { content: '📵'; }
    .fa-search::before { content: '🔍'; }
    .fa-filter::before { content: '🔽'; }
    .fa-moon::before { content: '🌙'; }
    .fa-sun::before { content: '☀️'; }
    .fa-home::before { content: '🏠'; }
    .fa-book::before { content: '📚'; }
    .fa-graduation-cap::before { content: '🎓'; }
    .fa-gamepad::before { content: '🎮'; }
    .fa-cog::before { content: '⚙️'; }
  </style>
  <style>
    :root {
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }

    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }

    .glass-effect {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .card-hover {
      transition: all 0.3s ease;
    }

    .card-hover:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .gradient-text {
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .pulse-animation {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .slide-in {
      animation: slideIn 0.5s ease-out;
    }

    @keyframes slideIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    canvas {
      border: 3px solid #667eea;
      border-radius: 10px;
      background: white;
    }

    .hidden { display: none; }

    .progress-bar {
      background: var(--primary-gradient);
      height: 6px;
      border-radius: 3px;
      transition: width 0.3s ease;
    }

    .theme-dark {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    .theme-dark .glass-effect {
      background: rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .sound-wave {
      display: inline-block;
      width: 4px;
      height: 20px;
      background: var(--primary-gradient);
      margin: 0 1px;
      animation: soundWave 1s infinite ease-in-out;
    }

    .sound-wave:nth-child(2) { animation-delay: 0.1s; }
    .sound-wave:nth-child(3) { animation-delay: 0.2s; }
    .sound-wave:nth-child(4) { animation-delay: 0.3s; }

    @keyframes soundWave {
      0%, 40%, 100% { transform: scaleY(0.4); }
      20% { transform: scaleY(1); }
    }
  </style>
</head>
<body class="font-sans transition-all duration-300" id="app-body">
  <!-- Theme Toggle & Settings -->
  <div class="fixed top-4 right-4 z-50 flex gap-2">
    <button id="themeToggle" class="glass-effect text-white p-3 rounded-full hover:bg-white hover:bg-opacity-20 transition-all">
      <i class="fas fa-moon"></i>
    </button>
    <button id="soundToggle" class="glass-effect text-white p-3 rounded-full hover:bg-white hover:bg-opacity-20 transition-all">
      <i class="fas fa-volume-up"></i>
    </button>
    <button id="musicToggle" class="glass-effect text-white p-3 rounded-full hover:bg-white hover:bg-opacity-20 transition-all">
      <i class="fas fa-music"></i>
    </button>
    <button id="testAudioButton" class="glass-effect text-white p-3 rounded-full hover:bg-white hover:bg-opacity-20 transition-all" title="Test Audio">
      <i class="fas fa-play"></i>
    </button>
  </div>

  <!-- Progress Bar -->
  <div class="fixed top-0 left-0 w-full h-1 bg-white bg-opacity-20 z-40">
    <div id="globalProgress" class="progress-bar w-0"></div>
  </div>

  <header class="glass-effect text-white p-6 shadow-lg">
    <div class="container mx-auto">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-4xl font-bold gradient-text mb-2">
            <i class="fas fa-torii-gate mr-3"></i>Learn Japanese
          </h1>
          <p class="text-white text-opacity-80">Master Japanese with advanced learning tools</p>
        </div>
        <div class="text-right">
          <div class="text-sm text-white text-opacity-80">Daily Progress</div>
          <div class="text-2xl font-bold" id="dailyScore">0/10</div>
          <div class="text-sm text-white text-opacity-80">Streak: <span id="streak">0</span> days</div>
        </div>
      </div>

      <nav class="mt-6 flex flex-wrap gap-2">
        <a href="#dashboard" class="nav-link glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
          <i class="fas fa-chart-line mr-2"></i>Dashboard
        </a>
        <a href="#dictionary" class="nav-link glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
          <i class="fas fa-book mr-2"></i>Dictionary
        </a>
        <a href="#writing" class="nav-link glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
          <i class="fas fa-pen mr-2"></i>Writing
        </a>
        <a href="#flashcards" class="nav-link glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
          <i class="fas fa-layer-group mr-2"></i>Flashcards
        </a>
        <a href="#sentence" class="nav-link glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
          <i class="fas fa-puzzle-piece mr-2"></i>Sentences
        </a>
        <a href="#quiz" class="nav-link glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
          <i class="fas fa-question-circle mr-2"></i>Quiz
        </a>
        <a href="#voice" class="nav-link glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all flex items-center">
          <i class="fas fa-microphone mr-2"></i>Voice Practice
        </a>
      </nav>
    </div>
  </header>

  <main class="container mx-auto p-6">
    <!-- Dashboard Section -->
    <section id="dashboard" class="mb-8 slide-in">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="glass-effect p-6 rounded-xl card-hover">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-white text-opacity-80 text-sm">Words Learned</p>
              <p class="text-3xl font-bold text-white" id="wordsLearned">0</p>
            </div>
            <i class="fas fa-book text-4xl text-white text-opacity-60"></i>
          </div>
        </div>
        <div class="glass-effect p-6 rounded-xl card-hover">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-white text-opacity-80 text-sm">Study Streak</p>
              <p class="text-3xl font-bold text-white" id="studyStreak">0</p>
            </div>
            <i class="fas fa-fire text-4xl text-white text-opacity-60"></i>
          </div>
        </div>
        <div class="glass-effect p-6 rounded-xl card-hover">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-white text-opacity-80 text-sm">Quiz Score</p>
              <p class="text-3xl font-bold text-white" id="averageScore">0%</p>
            </div>
            <i class="fas fa-trophy text-4xl text-white text-opacity-60"></i>
          </div>
        </div>
        <div class="glass-effect p-6 rounded-xl card-hover">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-white text-opacity-80 text-sm">Time Studied</p>
              <p class="text-3xl font-bold text-white" id="timeStudied">0h</p>
            </div>
            <i class="fas fa-clock text-4xl text-white text-opacity-60"></i>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="glass-effect p-6 rounded-xl">
          <h3 class="text-xl font-bold text-white mb-4">Learning Progress</h3>
          <div class="space-y-4">
            <div>
              <div class="flex justify-between text-white text-sm mb-1">
                <span>Hiragana</span>
                <span id="hiraganaProgress">0%</span>
              </div>
              <div class="w-full bg-white bg-opacity-20 rounded-full h-2">
                <div class="progress-bar h-2 rounded-full" style="width: 0%" id="hiraganaBar"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between text-white text-sm mb-1">
                <span>Katakana</span>
                <span id="katakanaProgress">0%</span>
              </div>
              <div class="w-full bg-white bg-opacity-20 rounded-full h-2">
                <div class="progress-bar h-2 rounded-full" style="width: 0%" id="katakanaBar"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between text-white text-sm mb-1">
                <span>Vocabulary</span>
                <span id="vocabProgress">0%</span>
              </div>
              <div class="w-full bg-white bg-opacity-20 rounded-full h-2">
                <div class="progress-bar h-2 rounded-full" style="width: 0%" id="vocabBar"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="glass-effect p-6 rounded-xl">
          <h3 class="text-xl font-bold text-white mb-4">Recent Activity</h3>
          <div id="recentActivity" class="space-y-3 text-white text-opacity-80">
            <p>Start learning to see your activity here!</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Dictionary Section -->
    <section id="dictionary" class="mb-8 slide-in hidden">
      <div class="glass-effect p-6 rounded-xl mb-6">
        <h2 class="text-2xl font-bold text-white mb-6">
          <i class="fas fa-book mr-3"></i>Interactive Dictionary
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <input type="text" id="searchInput" placeholder="Search Japanese words..."
                 class="glass-effect text-white placeholder-white placeholder-opacity-60 p-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:ring-opacity-50">

          <select id="categoryFilter" class="glass-effect text-white p-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:ring-opacity-50">
            <option value="">All Categories</option>
            <!-- Categories will be populated dynamically -->
          </select>

          <select id="difficultyFilter" class="glass-effect text-white p-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:ring-opacity-50">
            <option value="">All Levels</option>
            <option value="Beginner">Beginner</option>
            <option value="Intermediate">Intermediate</option>
            <option value="Advanced">Advanced</option>
          </select>
        </div>

        <div class="flex justify-between items-center mb-4">
          <p class="text-white text-opacity-80">
            <span id="resultsCount">0</span> words found |
            <span id="totalWords">0</span> total words in <span id="totalCategories">0</span> categories
          </p>
          <button id="favoriteFilter" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
            <i class="fas fa-heart mr-2"></i>Favorites Only
          </button>
        </div>
      </div>

      <div id="dictionaryResults" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Dictionary entries will be populated here -->
      </div>
    </section>

    <!-- Writing Practice Section -->
    <section id="writing" class="mb-8 slide-in hidden">
      <div class="glass-effect p-6 rounded-xl">
        <h2 class="text-2xl font-bold text-white mb-6">
          <i class="fas fa-pen mr-3"></i>Character Writing Practice
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div class="text-center">
            <div class="mb-6">
              <div class="flex justify-center gap-4 mb-4">
                <button id="hiraganaMode" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
                  Hiragana
                </button>
                <button id="katakanaMode" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
                  Katakana
                </button>
                <button id="kanjiMode" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
                  Basic Kanji
                </button>
              </div>

              <div class="glass-effect p-8 rounded-xl mb-4">
                <p class="text-6xl mb-2 text-white" id="currentChar">あ</p>
                <p class="text-xl text-white text-opacity-80" id="currentCharReading">a</p>
                <p class="text-lg text-white text-opacity-60" id="currentCharMeaning"></p>
              </div>

              <div class="flex justify-center gap-2 mb-4">
                <button id="showStrokeOrder" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
                  <i class="fas fa-route mr-2"></i>Show Strokes
                </button>
                <button id="playSound" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
                  <i class="fas fa-volume-up mr-2"></i>Pronounce
                </button>
              </div>
            </div>
          </div>

          <div class="text-center">
            <div class="mb-4">
              <p class="text-white text-opacity-80 mb-2">Practice writing here:</p>
              <canvas id="writingCanvas" width="300" height="300" class="mx-auto"></canvas>
            </div>

            <div class="flex justify-center gap-2 mb-4">
              <button id="clearCanvas" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
                <i class="fas fa-eraser mr-2"></i>Clear
              </button>
              <button id="checkWriting" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
                <i class="fas fa-check mr-2"></i>Check
              </button>
              <button id="nextChar" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
                <i class="fas fa-arrow-right mr-2"></i>Next
              </button>
            </div>

            <div id="writingFeedback" class="text-white text-opacity-80"></div>
          </div>
        </div>

        <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="glass-effect p-4 rounded-lg text-center">
            <p class="text-white text-opacity-80 text-sm">Characters Practiced</p>
            <p class="text-2xl font-bold text-white" id="charsPracticed">0</p>
          </div>
          <div class="glass-effect p-4 rounded-lg text-center">
            <p class="text-white text-opacity-80 text-sm">Accuracy</p>
            <p class="text-2xl font-bold text-white" id="writingAccuracy">0%</p>
          </div>
          <div class="glass-effect p-4 rounded-lg text-center">
            <p class="text-white text-opacity-80 text-sm">Current Level</p>
            <p class="text-2xl font-bold text-white" id="writingLevel">Beginner</p>
          </div>
          <div class="glass-effect p-4 rounded-lg text-center">
            <p class="text-white text-opacity-80 text-sm">Streak</p>
            <p class="text-2xl font-bold text-white" id="writingStreak">0</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Flashcards Section -->
    <section id="flashcards" class="mb-8 slide-in hidden">
      <div class="glass-effect p-6 rounded-xl">
        <h2 class="text-2xl font-bold text-white mb-6">
          <i class="fas fa-layer-group mr-3"></i>Smart Flashcards
        </h2>

        <div class="flex justify-center gap-4 mb-6">
          <button id="studyMode" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
            <i class="fas fa-book mr-2"></i>Study Mode
          </button>
          <button id="reviewMode" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
            <i class="fas fa-redo mr-2"></i>Review Mode
          </button>
          <button id="spacedRepetition" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
            <i class="fas fa-brain mr-2"></i>Spaced Repetition
          </button>
        </div>

        <div class="max-w-2xl mx-auto">
          <div class="glass-effect p-8 rounded-xl text-center mb-6 card-hover" id="flashcardContainer">
            <div class="mb-4">
              <span class="text-white text-opacity-60 text-sm">Card <span id="currentCardNumber">1</span> of <span id="totalCards">3</span></span>
            </div>

            <div id="flashcardFront" class="flashcard-side">
              <p class="text-5xl mb-4 text-white" id="flashcardWord">りんご</p>
              <p class="text-2xl mb-4 text-white text-opacity-80" id="flashcardReading">ringo</p>
              <div class="flex justify-center mb-4">
                <button id="speakJapanese" class="glass-effect text-white p-3 rounded-full hover:bg-white hover:bg-opacity-20 transition-all">
                  <div class="sound-wave"></div>
                  <div class="sound-wave"></div>
                  <div class="sound-wave"></div>
                  <div class="sound-wave"></div>
                </button>
              </div>
            </div>

            <div id="flashcardBack" class="flashcard-side hidden">
              <p class="text-3xl mb-4 text-white" id="flashcardEnglish">Apple</p>

              <div class="mb-4">
                <div class="flex justify-between items-center mb-2">
                  <p class="text-white text-opacity-80">Example:</p>
                  <div class="flex gap-1">
                    <button id="speakExampleJP" class="text-white text-opacity-60 hover:text-white transition-colors text-sm">
                      <i class="fas fa-volume-up"></i> JP
                    </button>
                    <button id="speakExampleEN" class="text-white text-opacity-60 hover:text-white transition-colors text-sm">
                      <i class="fas fa-volume-up"></i> EN
                    </button>
                  </div>
                </div>
                <p class="text-xl mb-2 text-white text-opacity-80 cursor-pointer" id="flashcardExample" onclick="speakFlashcardExample()">りんごを食べます。</p>
                <p class="text-lg text-white text-opacity-60 cursor-pointer" id="flashcardExampleTranslation" onclick="speakFlashcardExampleTranslation()">I eat an apple.</p>
              </div>

              <div class="flex justify-center gap-2 mt-6">
                <button id="difficultyHard" class="glass-effect text-red-300 px-4 py-2 rounded-lg hover:bg-red-500 hover:bg-opacity-20 transition-all">
                  <i class="fas fa-times mr-2"></i>Hard
                </button>
                <button id="difficultyMedium" class="glass-effect text-yellow-300 px-4 py-2 rounded-lg hover:bg-yellow-500 hover:bg-opacity-20 transition-all">
                  <i class="fas fa-minus mr-2"></i>Medium
                </button>
                <button id="difficultyEasy" class="glass-effect text-green-300 px-4 py-2 rounded-lg hover:bg-green-500 hover:bg-opacity-20 transition-all">
                  <i class="fas fa-check mr-2"></i>Easy
                </button>
              </div>
            </div>

            <button id="flipCard" class="glass-effect text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all mt-4">
              <i class="fas fa-sync-alt mr-2"></i>Flip Card
            </button>
          </div>

          <div class="flex justify-center gap-4">
            <button id="prevFlashcard" class="glass-effect text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
              <i class="fas fa-arrow-left mr-2"></i>Previous
            </button>
            <button id="favoriteCard" class="glass-effect text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
              <i class="fas fa-heart mr-2"></i>Favorite
            </button>
            <button id="speakEnglish" class="glass-effect text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
              <i class="fas fa-volume-up mr-2"></i>English
            </button>
            <button id="nextFlashcard" class="glass-effect text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
              <i class="fas fa-arrow-right mr-2"></i>Next
            </button>
          </div>
        </div>

        <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="glass-effect p-4 rounded-lg text-center">
            <p class="text-white text-opacity-80 text-sm">Cards Studied</p>
            <p class="text-2xl font-bold text-white" id="cardsStudied">0</p>
          </div>
          <div class="glass-effect p-4 rounded-lg text-center">
            <p class="text-white text-opacity-80 text-sm">Success Rate</p>
            <p class="text-2xl font-bold text-white" id="successRate">0%</p>
          </div>
          <div class="glass-effect p-4 rounded-lg text-center">
            <p class="text-white text-opacity-80 text-sm">Due for Review</p>
            <p class="text-2xl font-bold text-white" id="dueCards">0</p>
          </div>
          <div class="glass-effect p-4 rounded-lg text-center">
            <p class="text-white text-opacity-80 text-sm">Mastered</p>
            <p class="text-2xl font-bold text-white" id="masteredCards">0</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Sentence Making Section -->
    <section id="sentence" class="mb-8 slide-in hidden">
      <div class="glass-effect p-6 rounded-xl">
        <h2 class="text-2xl font-bold text-white mb-6">
          <i class="fas fa-puzzle-piece mr-3"></i>Sentence Construction
        </h2>

        <div class="max-w-4xl mx-auto">
          <div class="glass-effect p-6 rounded-xl mb-6">
            <div class="text-center mb-4">
              <p class="text-white text-opacity-80 mb-2">Create this sentence:</p>
              <p class="text-2xl text-white mb-2" id="sentencePrompt">"I eat an apple."</p>
              <p class="text-lg text-white text-opacity-60" id="sentenceHint">Hint: Subject + Object + Verb</p>
            </div>
          </div>

          <div class="mb-6">
            <p class="text-white text-opacity-80 mb-4">Word Bank - Drag words to build your sentence:</p>
            <div id="wordBank" class="flex flex-wrap gap-3 justify-center mb-6">
              <!-- Word tiles will be populated here -->
            </div>
          </div>

          <div class="glass-effect p-6 rounded-xl mb-6">
            <p class="text-white text-opacity-80 mb-2 text-center">Your Sentence:</p>
            <div id="sentenceArea" class="min-h-[80px] border-2 border-dashed border-white border-opacity-30 rounded-lg p-4 flex flex-wrap gap-2 justify-center items-center">
              <span class="text-white text-opacity-50">Drop words here...</span>
            </div>
          </div>

          <div class="flex justify-center gap-4 mb-6">
            <button id="clearSentence" class="glass-effect text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
              <i class="fas fa-trash mr-2"></i>Clear
            </button>
            <button id="checkSentence" class="glass-effect text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
              <i class="fas fa-check mr-2"></i>Check Answer
            </button>
            <button id="speakSentence" class="glass-effect text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
              <i class="fas fa-volume-up mr-2"></i>Speak
            </button>
            <button id="nextSentence" class="glass-effect text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
              <i class="fas fa-arrow-right mr-2"></i>Next
            </button>
          </div>

          <div id="sentenceFeedback" class="text-center text-white"></div>
        </div>
      </div>
    </section>

    <!-- Quiz Section -->
    <section id="quiz" class="mb-8 slide-in hidden">
      <div class="glass-effect p-6 rounded-xl">
        <h2 class="text-2xl font-bold text-white mb-6">
          <i class="fas fa-question-circle mr-3"></i>Interactive Quiz
        </h2>

        <div class="max-w-3xl mx-auto">
          <div class="flex justify-center gap-4 mb-6">
            <button id="vocabQuiz" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
              <i class="fas fa-book mr-2"></i>Vocabulary
            </button>
            <button id="readingQuiz" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
              <i class="fas fa-eye mr-2"></i>Reading
            </button>
            <button id="listeningQuiz" class="glass-effect text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
              <i class="fas fa-headphones mr-2"></i>Listening
            </button>
          </div>

          <div class="glass-effect p-8 rounded-xl mb-6">
            <div class="text-center mb-6">
              <div class="text-white text-opacity-60 mb-2">Question <span id="questionNumber">1</span> of <span id="totalQuestions">10</span></div>
              <div class="w-full bg-white bg-opacity-20 rounded-full h-2 mb-4">
                <div class="progress-bar h-2 rounded-full" style="width: 10%" id="quizProgress"></div>
              </div>
            </div>

            <div id="quizQuestion" class="text-center mb-8">
              <p class="text-3xl text-white mb-4" id="questionText">りんご</p>
              <p class="text-lg text-white text-opacity-80" id="questionPrompt">What does this word mean?</p>
            </div>

            <div id="quizOptions" class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Quiz options will be populated here -->
            </div>

            <div id="quizFeedback" class="text-center mt-6 text-white"></div>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="glass-effect p-4 rounded-lg text-center">
              <p class="text-white text-opacity-80 text-sm">Score</p>
              <p class="text-2xl font-bold text-white" id="quizScore">0/0</p>
            </div>
            <div class="glass-effect p-4 rounded-lg text-center">
              <p class="text-white text-opacity-80 text-sm">Accuracy</p>
              <p class="text-2xl font-bold text-white" id="quizAccuracy">0%</p>
            </div>
            <div class="glass-effect p-4 rounded-lg text-center">
              <p class="text-white text-opacity-80 text-sm">Time</p>
              <p class="text-2xl font-bold text-white" id="quizTime">0:00</p>
            </div>
            <div class="glass-effect p-4 rounded-lg text-center">
              <p class="text-white text-opacity-80 text-sm">Best Score</p>
              <p class="text-2xl font-bold text-white" id="bestScore">0%</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Voice Practice Section -->
    <section id="voice" class="mb-8 slide-in hidden">
      <div class="glass-effect p-6 rounded-xl">
        <h2 class="text-2xl font-bold text-white mb-6">
          <i class="fas fa-microphone mr-3"></i>Voice Practice
        </h2>

        <div class="max-w-3xl mx-auto">
          <div class="glass-effect p-8 rounded-xl mb-6 text-center">
            <div class="mb-6">
              <p class="text-white text-opacity-80 mb-2">Practice saying:</p>
              <p class="text-4xl text-white mb-2" id="voicePracticeWord">こんにちは</p>
              <p class="text-xl text-white text-opacity-80" id="voicePracticeReading">konnichiwa</p>
              <p class="text-lg text-white text-opacity-60" id="voicePracticeMeaning">Hello</p>
            </div>

            <div class="flex justify-center gap-4 mb-6">
              <button id="playTargetAudio" class="glass-effect text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
                <i class="fas fa-play mr-2"></i>Listen
              </button>
              <button id="startRecording" class="glass-effect text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all">
                <i class="fas fa-microphone mr-2"></i>Record
              </button>
              <button id="stopRecording" class="glass-effect text-white px-6 py-3 rounded-lg hover:bg-white hover:bg-opacity-20 transition-all hidden">
                <i class="fas fa-stop mr-2"></i>Stop
              </button>
            </div>

            <div id="recordingStatus" class="text-white text-opacity-80 mb-4"></div>
            <div id="pronunciationFeedback" class="text-white"></div>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="glass-effect p-4 rounded-lg text-center">
              <p class="text-white text-opacity-80 text-sm">Words Practiced</p>
              <p class="text-2xl font-bold text-white" id="wordsPracticed">0</p>
            </div>
            <div class="glass-effect p-4 rounded-lg text-center">
              <p class="text-white text-opacity-80 text-sm">Pronunciation Score</p>
              <p class="text-2xl font-bold text-white" id="pronunciationScore">0%</p>
            </div>
            <div class="glass-effect p-4 rounded-lg text-center">
              <p class="text-white text-opacity-80 text-sm">Session Time</p>
              <p class="text-2xl font-bold text-white" id="sessionTime">0:00</p>
            </div>
            <div class="glass-effect p-4 rounded-lg text-center">
              <p class="text-white text-opacity-80 text-sm">Improvement</p>
              <p class="text-2xl font-bold text-white" id="improvement">+0%</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <script>
    // Enhanced dictionary with 400+ words across 20 categories
    const dictionary = [
      // Food (20 words)
      { word: "りんご", reading: "ringo", english: "Apple", category: "Food", difficulty: "Beginner", example: "りんごを食べます。", exampleTranslation: "I eat an apple." },
      { word: "みず", reading: "mizu", english: "Water", category: "Food", difficulty: "Beginner", example: "みずを飲みます。", exampleTranslation: "I drink water." },
      { word: "パン", reading: "pan", english: "Bread", category: "Food", difficulty: "Beginner", example: "パンを食べます。", exampleTranslation: "I eat bread." },
      { word: "さかな", reading: "sakana", english: "Fish", category: "Food", difficulty: "Beginner", example: "さかなを食べます。", exampleTranslation: "I eat fish." },
      { word: "にく", reading: "niku", english: "Meat", category: "Food", difficulty: "Beginner", example: "にくを食べます。", exampleTranslation: "I eat meat." },
      { word: "やさい", reading: "yasai", english: "Vegetables", category: "Food", difficulty: "Beginner", example: "やさいを食べます。", exampleTranslation: "I eat vegetables." },
      { word: "ごはん", reading: "gohan", english: "Rice/Meal", category: "Food", difficulty: "Beginner", example: "ごはんを食べます。", exampleTranslation: "I eat rice." },
      { word: "おちゃ", reading: "ocha", english: "Tea", category: "Food", difficulty: "Beginner", example: "おちゃを飲みます。", exampleTranslation: "I drink tea." },
      { word: "コーヒー", reading: "koohii", english: "Coffee", category: "Food", difficulty: "Beginner", example: "コーヒーを飲みます。", exampleTranslation: "I drink coffee." },
      { word: "たまご", reading: "tamago", english: "Egg", category: "Food", difficulty: "Beginner", example: "たまごを食べます。", exampleTranslation: "I eat eggs." },
      { word: "ぎゅうにゅう", reading: "gyuunyuu", english: "Milk", category: "Food", difficulty: "Intermediate", example: "ぎゅうにゅうを飲みます。", exampleTranslation: "I drink milk." },
      { word: "くだもの", reading: "kudamono", english: "Fruit", category: "Food", difficulty: "Intermediate", example: "くだものを食べます。", exampleTranslation: "I eat fruit." },
      { word: "ケーキ", reading: "keeki", english: "Cake", category: "Food", difficulty: "Beginner", example: "ケーキを食べます。", exampleTranslation: "I eat cake." },
      { word: "アイスクリーム", reading: "aisukuriimu", english: "Ice cream", category: "Food", difficulty: "Beginner", example: "アイスクリームを食べます。", exampleTranslation: "I eat ice cream." },
      { word: "チョコレート", reading: "chokoreeto", english: "Chocolate", category: "Food", difficulty: "Beginner", example: "チョコレートを食べます。", exampleTranslation: "I eat chocolate." },
      { word: "スープ", reading: "suupu", english: "Soup", category: "Food", difficulty: "Beginner", example: "スープを飲みます。", exampleTranslation: "I drink soup." },
      { word: "サラダ", reading: "sarada", english: "Salad", category: "Food", difficulty: "Beginner", example: "サラダを食べます。", exampleTranslation: "I eat salad." },
      { word: "ラーメン", reading: "raamen", english: "Ramen", category: "Food", difficulty: "Beginner", example: "ラーメンを食べます。", exampleTranslation: "I eat ramen." },
      { word: "すし", reading: "sushi", english: "Sushi", category: "Food", difficulty: "Beginner", example: "すしを食べます。", exampleTranslation: "I eat sushi." },
      { word: "おかし", reading: "okashi", english: "Sweets", category: "Food", difficulty: "Intermediate", example: "おかしを食べます。", exampleTranslation: "I eat sweets." },

      // Greetings (20 words)
      { word: "おはよう", reading: "ohayou", english: "Good morning", category: "Greetings", difficulty: "Beginner", example: "おはようございます。", exampleTranslation: "Good morning." },
      { word: "こんにちは", reading: "konnichiwa", english: "Hello", category: "Greetings", difficulty: "Beginner", example: "こんにちは、元気ですか。", exampleTranslation: "Hello, how are you?" },
      { word: "こんばんは", reading: "konbanwa", english: "Good evening", category: "Greetings", difficulty: "Beginner", example: "こんばんは、お疲れ様です。", exampleTranslation: "Good evening, thank you for your hard work." },
      { word: "ありがとう", reading: "arigatou", english: "Thank you", category: "Greetings", difficulty: "Beginner", example: "ありがとうございます。", exampleTranslation: "Thank you very much." },
      { word: "すみません", reading: "sumimasen", english: "Excuse me/Sorry", category: "Greetings", difficulty: "Beginner", example: "すみません、質問があります。", exampleTranslation: "Excuse me, I have a question." },
      { word: "さようなら", reading: "sayounara", english: "Goodbye", category: "Greetings", difficulty: "Beginner", example: "さようなら、また明日。", exampleTranslation: "Goodbye, see you tomorrow." },
      { word: "はじめまして", reading: "hajimemashite", english: "Nice to meet you", category: "Greetings", difficulty: "Intermediate", example: "はじめまして、よろしくお願いします。", exampleTranslation: "Nice to meet you, please treat me favorably." },
      { word: "げんき", reading: "genki", english: "Healthy/Fine", category: "Greetings", difficulty: "Beginner", example: "元気ですか。", exampleTranslation: "How are you?" },
      { word: "いらっしゃいませ", reading: "irasshaimase", english: "Welcome", category: "Greetings", difficulty: "Intermediate", example: "いらっしゃいませ、こちらへどうぞ。", exampleTranslation: "Welcome, please come this way." },
      { word: "おつかれさま", reading: "otsukaresama", english: "Thank you for your hard work", category: "Greetings", difficulty: "Intermediate", example: "お疲れ様でした。", exampleTranslation: "Thank you for your hard work." },
      { word: "ただいま", reading: "tadaima", english: "I'm home", category: "Greetings", difficulty: "Beginner", example: "ただいま帰りました。", exampleTranslation: "I'm home." },
      { word: "おかえり", reading: "okaeri", english: "Welcome home", category: "Greetings", difficulty: "Beginner", example: "おかえりなさい。", exampleTranslation: "Welcome home." },
      { word: "いただきます", reading: "itadakimasu", english: "Let's eat", category: "Greetings", difficulty: "Intermediate", example: "いただきます。", exampleTranslation: "Let's eat (before eating)." },
      { word: "ごちそうさま", reading: "gochisousama", english: "Thank you for the meal", category: "Greetings", difficulty: "Intermediate", example: "ごちそうさまでした。", exampleTranslation: "Thank you for the meal." },
      { word: "おめでとう", reading: "omedetou", english: "Congratulations", category: "Greetings", difficulty: "Intermediate", example: "おめでとうございます。", exampleTranslation: "Congratulations." },
      { word: "がんばって", reading: "ganbatte", english: "Good luck/Do your best", category: "Greetings", difficulty: "Intermediate", example: "がんばってください。", exampleTranslation: "Please do your best." },
      { word: "しつれいします", reading: "shitsurei shimasu", english: "Excuse me (leaving)", category: "Greetings", difficulty: "Advanced", example: "失礼します。", exampleTranslation: "Excuse me (when leaving)." },
      { word: "よろしく", reading: "yoroshiku", english: "Please treat me favorably", category: "Greetings", difficulty: "Intermediate", example: "よろしくお願いします。", exampleTranslation: "Please treat me favorably." },
      { word: "おやすみ", reading: "oyasumi", english: "Good night", category: "Greetings", difficulty: "Beginner", example: "おやすみなさい。", exampleTranslation: "Good night." },
      { word: "いってきます", reading: "ittekimasu", english: "I'm leaving", category: "Greetings", difficulty: "Intermediate", example: "いってきます。", exampleTranslation: "I'm leaving (and will return)." },

      // Objects (20 words)
      { word: "本", reading: "hon", english: "Book", category: "Objects", difficulty: "Beginner", example: "本を読みます。", exampleTranslation: "I read a book." },
      { word: "ペン", reading: "pen", english: "Pen", category: "Objects", difficulty: "Beginner", example: "ペンで書きます。", exampleTranslation: "I write with a pen." },
      { word: "かみ", reading: "kami", english: "Paper", category: "Objects", difficulty: "Beginner", example: "紙に書きます。", exampleTranslation: "I write on paper." },
      { word: "つくえ", reading: "tsukue", english: "Desk", category: "Objects", difficulty: "Beginner", example: "机で勉強します。", exampleTranslation: "I study at the desk." },
      { word: "いす", reading: "isu", english: "Chair", category: "Objects", difficulty: "Beginner", example: "椅子に座ります。", exampleTranslation: "I sit on the chair." },
      { word: "でんわ", reading: "denwa", english: "Telephone", category: "Objects", difficulty: "Beginner", example: "電話をかけます。", exampleTranslation: "I make a phone call." },
      { word: "テレビ", reading: "terebi", english: "Television", category: "Objects", difficulty: "Beginner", example: "テレビを見ます。", exampleTranslation: "I watch TV." },
      { word: "コンピューター", reading: "konpyuutaa", english: "Computer", category: "Objects", difficulty: "Beginner", example: "コンピューターを使います。", exampleTranslation: "I use a computer." },
      { word: "かばん", reading: "kaban", english: "Bag", category: "Objects", difficulty: "Beginner", example: "かばんを持ちます。", exampleTranslation: "I carry a bag." },
      { word: "とけい", reading: "tokei", english: "Clock/Watch", category: "Objects", difficulty: "Beginner", example: "時計を見ます。", exampleTranslation: "I look at the clock." },
      { word: "くるま", reading: "kuruma", english: "Car", category: "Objects", difficulty: "Beginner", example: "車で行きます。", exampleTranslation: "I go by car." },
      { word: "じてんしゃ", reading: "jitensha", english: "Bicycle", category: "Objects", difficulty: "Intermediate", example: "自転車に乗ります。", exampleTranslation: "I ride a bicycle." },
      { word: "かぎ", reading: "kagi", english: "Key", category: "Objects", difficulty: "Beginner", example: "鍵をかけます。", exampleTranslation: "I lock with a key." },
      { word: "まど", reading: "mado", english: "Window", category: "Objects", difficulty: "Beginner", example: "窓を開けます。", exampleTranslation: "I open the window." },
      { word: "ドア", reading: "doa", english: "Door", category: "Objects", difficulty: "Beginner", example: "ドアを閉めます。", exampleTranslation: "I close the door." },
      { word: "ベッド", reading: "beddo", english: "Bed", category: "Objects", difficulty: "Beginner", example: "ベッドで寝ます。", exampleTranslation: "I sleep in bed." },
      { word: "ふとん", reading: "futon", english: "Futon", category: "Objects", difficulty: "Intermediate", example: "布団で寝ます。", exampleTranslation: "I sleep on a futon." },
      { word: "エアコン", reading: "eakon", english: "Air conditioner", category: "Objects", difficulty: "Intermediate", example: "エアコンをつけます。", exampleTranslation: "I turn on the air conditioner." },
      { word: "れいぞうこ", reading: "reizouko", english: "Refrigerator", category: "Objects", difficulty: "Intermediate", example: "冷蔵庫に入れます。", exampleTranslation: "I put it in the refrigerator." },
      { word: "せんたくき", reading: "sentakuki", english: "Washing machine", category: "Objects", difficulty: "Advanced", example: "洗濯機で洗います。", exampleTranslation: "I wash with the washing machine." },

      // Family (20 words)
      { word: "かぞく", reading: "kazoku", english: "Family", category: "Family", difficulty: "Beginner", example: "家族と住んでいます。", exampleTranslation: "I live with my family." },
      { word: "おとうさん", reading: "otousan", english: "Father", category: "Family", difficulty: "Beginner", example: "お父さんは会社員です。", exampleTranslation: "My father is a company employee." },
      { word: "おかあさん", reading: "okaasan", english: "Mother", category: "Family", difficulty: "Beginner", example: "お母さんは料理が上手です。", exampleTranslation: "My mother is good at cooking." },
      { word: "あに", reading: "ani", english: "Older brother", category: "Family", difficulty: "Intermediate", example: "兄は大学生です。", exampleTranslation: "My older brother is a university student." },
      { word: "あね", reading: "ane", english: "Older sister", category: "Family", difficulty: "Intermediate", example: "姉は看護師です。", exampleTranslation: "My older sister is a nurse." },
      { word: "おとうと", reading: "otouto", english: "Younger brother", category: "Family", difficulty: "Intermediate", example: "弟は高校生です。", exampleTranslation: "My younger brother is a high school student." },
      { word: "いもうと", reading: "imouto", english: "Younger sister", category: "Family", difficulty: "Intermediate", example: "妹は中学生です。", exampleTranslation: "My younger sister is a middle school student." },
      { word: "おじいさん", reading: "ojiisan", english: "Grandfather", category: "Family", difficulty: "Intermediate", example: "おじいさんは元気です。", exampleTranslation: "My grandfather is healthy." },
      { word: "おばあさん", reading: "obaasan", english: "Grandmother", category: "Family", difficulty: "Intermediate", example: "おばあさんは優しいです。", exampleTranslation: "My grandmother is kind." },
      { word: "おじさん", reading: "ojisan", english: "Uncle", category: "Family", difficulty: "Intermediate", example: "おじさんは医者です。", exampleTranslation: "My uncle is a doctor." },
      { word: "おばさん", reading: "obasan", english: "Aunt", category: "Family", difficulty: "Intermediate", example: "おばさんは先生です。", exampleTranslation: "My aunt is a teacher." },
      { word: "いとこ", reading: "itoko", english: "Cousin", category: "Family", difficulty: "Intermediate", example: "いとこと遊びます。", exampleTranslation: "I play with my cousin." },
      { word: "むすこ", reading: "musuko", english: "Son", category: "Family", difficulty: "Intermediate", example: "息子は小学生です。", exampleTranslation: "My son is an elementary school student." },
      { word: "むすめ", reading: "musume", english: "Daughter", category: "Family", difficulty: "Intermediate", example: "娘はピアノを習っています。", exampleTranslation: "My daughter is learning piano." },
      { word: "しゅじん", reading: "shujin", english: "Husband", category: "Family", difficulty: "Advanced", example: "主人は会社で働いています。", exampleTranslation: "My husband works at a company." },
      { word: "つま", reading: "tsuma", english: "Wife", category: "Family", difficulty: "Advanced", example: "妻は料理が得意です。", exampleTranslation: "My wife is good at cooking." },
      { word: "りょうしん", reading: "ryoushin", english: "Parents", category: "Family", difficulty: "Advanced", example: "両親に会いに行きます。", exampleTranslation: "I'm going to see my parents." },
      { word: "きょうだい", reading: "kyoudai", english: "Siblings", category: "Family", difficulty: "Advanced", example: "兄弟は三人います。", exampleTranslation: "I have three siblings." },
      { word: "まご", reading: "mago", english: "Grandchild", category: "Family", difficulty: "Advanced", example: "孫がかわいいです。", exampleTranslation: "My grandchild is cute." },
      { word: "しんせき", reading: "shinseki", english: "Relatives", category: "Family", difficulty: "Advanced", example: "親戚が集まります。", exampleTranslation: "The relatives gather." },

      // Colors (20 words)
      { word: "あか", reading: "aka", english: "Red", category: "Colors", difficulty: "Beginner", example: "赤い花がきれいです。", exampleTranslation: "The red flowers are beautiful." },
      { word: "あお", reading: "ao", english: "Blue", category: "Colors", difficulty: "Beginner", example: "青い空が好きです。", exampleTranslation: "I like the blue sky." },
      { word: "きいろ", reading: "kiiro", english: "Yellow", category: "Colors", difficulty: "Beginner", example: "黄色いバナナを食べます。", exampleTranslation: "I eat yellow bananas." },
      { word: "みどり", reading: "midori", english: "Green", category: "Colors", difficulty: "Beginner", example: "緑の葉っぱがきれいです。", exampleTranslation: "The green leaves are beautiful." },
      { word: "しろ", reading: "shiro", english: "White", category: "Colors", difficulty: "Beginner", example: "白い雲を見ます。", exampleTranslation: "I look at white clouds." },
      { word: "くろ", reading: "kuro", english: "Black", category: "Colors", difficulty: "Beginner", example: "黒い猫がいます。", exampleTranslation: "There is a black cat." },
      { word: "ピンク", reading: "pinku", english: "Pink", category: "Colors", difficulty: "Beginner", example: "ピンクの花が咲いています。", exampleTranslation: "Pink flowers are blooming." },
      { word: "オレンジ", reading: "orenji", english: "Orange", category: "Colors", difficulty: "Beginner", example: "オレンジ色の夕日です。", exampleTranslation: "It's an orange sunset." },
      { word: "むらさき", reading: "murasaki", english: "Purple", category: "Colors", difficulty: "Intermediate", example: "紫の服を着ています。", exampleTranslation: "I'm wearing purple clothes." },
      { word: "ちゃいろ", reading: "chairo", english: "Brown", category: "Colors", difficulty: "Intermediate", example: "茶色い犬がいます。", exampleTranslation: "There is a brown dog." },
      { word: "はいいろ", reading: "haiiro", english: "Gray", category: "Colors", difficulty: "Intermediate", example: "灰色の雲が出ています。", exampleTranslation: "Gray clouds are out." },
      { word: "きん", reading: "kin", english: "Gold", category: "Colors", difficulty: "Advanced", example: "金色のアクセサリーです。", exampleTranslation: "It's gold accessories." },
      { word: "ぎん", reading: "gin", english: "Silver", category: "Colors", difficulty: "Advanced", example: "銀色の車です。", exampleTranslation: "It's a silver car." },
      { word: "あかるい", reading: "akarui", english: "Bright", category: "Colors", difficulty: "Intermediate", example: "明るい色が好きです。", exampleTranslation: "I like bright colors." },
      { word: "くらい", reading: "kurai", english: "Dark", category: "Colors", difficulty: "Intermediate", example: "暗い色は嫌いです。", exampleTranslation: "I don't like dark colors." },
      { word: "こい", reading: "koi", english: "Deep/Dark (color)", category: "Colors", difficulty: "Advanced", example: "濃い青色です。", exampleTranslation: "It's a deep blue color." },
      { word: "うすい", reading: "usui", english: "Light/Pale", category: "Colors", difficulty: "Advanced", example: "薄いピンク色です。", exampleTranslation: "It's a light pink color." },
      { word: "あざやか", reading: "azayaka", english: "Vivid", category: "Colors", difficulty: "Advanced", example: "鮮やかな色です。", exampleTranslation: "It's a vivid color." },
      { word: "じみ", reading: "jimi", english: "Plain/Subdued", category: "Colors", difficulty: "Advanced", example: "地味な色を選びます。", exampleTranslation: "I choose subdued colors." },
      { word: "カラフル", reading: "karafuru", english: "Colorful", category: "Colors", difficulty: "Intermediate", example: "カラフルな絵です。", exampleTranslation: "It's a colorful picture." },

      // Numbers (20 words)
      { word: "ゼロ", reading: "zero", english: "Zero", category: "Numbers", difficulty: "Beginner", example: "ゼロから始めます。", exampleTranslation: "I start from zero." },
      { word: "いち", reading: "ichi", english: "One", category: "Numbers", difficulty: "Beginner", example: "一つください。", exampleTranslation: "Please give me one." },
      { word: "に", reading: "ni", english: "Two", category: "Numbers", difficulty: "Beginner", example: "二つあります。", exampleTranslation: "There are two." },
      { word: "さん", reading: "san", english: "Three", category: "Numbers", difficulty: "Beginner", example: "三人います。", exampleTranslation: "There are three people." },
      { word: "よん", reading: "yon", english: "Four", category: "Numbers", difficulty: "Beginner", example: "四時です。", exampleTranslation: "It's four o'clock." },
      { word: "ご", reading: "go", english: "Five", category: "Numbers", difficulty: "Beginner", example: "五分待ってください。", exampleTranslation: "Please wait five minutes." },
      { word: "ろく", reading: "roku", english: "Six", category: "Numbers", difficulty: "Beginner", example: "六時に起きます。", exampleTranslation: "I wake up at six o'clock." },
      { word: "なな", reading: "nana", english: "Seven", category: "Numbers", difficulty: "Beginner", example: "七日間休みます。", exampleTranslation: "I rest for seven days." },
      { word: "はち", reading: "hachi", english: "Eight", category: "Numbers", difficulty: "Beginner", example: "八月です。", exampleTranslation: "It's August." },
      { word: "きゅう", reading: "kyuu", english: "Nine", category: "Numbers", difficulty: "Beginner", example: "九時に寝ます。", exampleTranslation: "I sleep at nine o'clock." },
      { word: "じゅう", reading: "juu", english: "Ten", category: "Numbers", difficulty: "Beginner", example: "十個あります。", exampleTranslation: "There are ten." },
      { word: "ひゃく", reading: "hyaku", english: "Hundred", category: "Numbers", difficulty: "Intermediate", example: "百円です。", exampleTranslation: "It's 100 yen." },
      { word: "せん", reading: "sen", english: "Thousand", category: "Numbers", difficulty: "Intermediate", example: "千円札です。", exampleTranslation: "It's a 1000 yen bill." },
      { word: "まん", reading: "man", english: "Ten thousand", category: "Numbers", difficulty: "Intermediate", example: "一万円です。", exampleTranslation: "It's 10,000 yen." },
      { word: "おく", reading: "oku", english: "Hundred million", category: "Numbers", difficulty: "Advanced", example: "一億人います。", exampleTranslation: "There are 100 million people." },
      { word: "はん", reading: "han", english: "Half", category: "Numbers", difficulty: "Intermediate", example: "三時半です。", exampleTranslation: "It's 3:30." },
      { word: "ばん", reading: "ban", english: "Number", category: "Numbers", difficulty: "Intermediate", example: "電話番号を教えてください。", exampleTranslation: "Please tell me your phone number." },
      { word: "だい", reading: "dai", english: "Number (ordinal)", category: "Numbers", difficulty: "Advanced", example: "第一番です。", exampleTranslation: "It's number one." },
      { word: "かず", reading: "kazu", english: "Number/Amount", category: "Numbers", difficulty: "Advanced", example: "数を数えます。", exampleTranslation: "I count numbers." },
      { word: "すうじ", reading: "suuji", english: "Numeral", category: "Numbers", difficulty: "Advanced", example: "数字を書きます。", exampleTranslation: "I write numerals." },

      // Time (20 words)
      { word: "じかん", reading: "jikan", english: "Time", category: "Time", difficulty: "Beginner", example: "時間がありません。", exampleTranslation: "I don't have time." },
      { word: "いま", reading: "ima", english: "Now", category: "Time", difficulty: "Beginner", example: "今、忙しいです。", exampleTranslation: "I'm busy now." },
      { word: "きょう", reading: "kyou", english: "Today", category: "Time", difficulty: "Beginner", example: "今日は暑いです。", exampleTranslation: "It's hot today." },
      { word: "あした", reading: "ashita", english: "Tomorrow", category: "Time", difficulty: "Beginner", example: "明日会いましょう。", exampleTranslation: "Let's meet tomorrow." },
      { word: "きのう", reading: "kinou", english: "Yesterday", category: "Time", difficulty: "Beginner", example: "昨日映画を見ました。", exampleTranslation: "I watched a movie yesterday." },
      { word: "あさ", reading: "asa", english: "Morning", category: "Time", difficulty: "Beginner", example: "朝ごはんを食べます。", exampleTranslation: "I eat breakfast." },
      { word: "ひる", reading: "hiru", english: "Noon/Daytime", category: "Time", difficulty: "Beginner", example: "昼ごはんを食べます。", exampleTranslation: "I eat lunch." },
      { word: "よる", reading: "yoru", english: "Night", category: "Time", difficulty: "Beginner", example: "夜遅く帰ります。", exampleTranslation: "I return home late at night." },
      { word: "しゅう", reading: "shuu", english: "Week", category: "Time", difficulty: "Intermediate", example: "来週会いましょう。", exampleTranslation: "Let's meet next week." },
      { word: "つき", reading: "tsuki", english: "Month", category: "Time", difficulty: "Beginner", example: "来月日本に行きます。", exampleTranslation: "I'm going to Japan next month." },
      { word: "とし", reading: "toshi", english: "Year", category: "Time", difficulty: "Beginner", example: "来年結婚します。", exampleTranslation: "I'm getting married next year." },
      { word: "ふん", reading: "fun", english: "Minute", category: "Time", difficulty: "Beginner", example: "五分待ってください。", exampleTranslation: "Please wait five minutes." },
      { word: "びょう", reading: "byou", english: "Second", category: "Time", difficulty: "Intermediate", example: "三十秒です。", exampleTranslation: "It's thirty seconds." },
      { word: "まえ", reading: "mae", english: "Before/Ago", category: "Time", difficulty: "Intermediate", example: "三時間前です。", exampleTranslation: "It was three hours ago." },
      { word: "あと", reading: "ato", english: "After/Later", category: "Time", difficulty: "Intermediate", example: "後で電話します。", exampleTranslation: "I'll call later." },
      { word: "はやい", reading: "hayai", english: "Early/Fast", category: "Time", difficulty: "Intermediate", example: "早く起きます。", exampleTranslation: "I wake up early." },
      { word: "おそい", reading: "osoi", english: "Late/Slow", category: "Time", difficulty: "Intermediate", example: "遅く帰ります。", exampleTranslation: "I return home late." },
      { word: "いつ", reading: "itsu", english: "When", category: "Time", difficulty: "Beginner", example: "いつ行きますか。", exampleTranslation: "When are you going?" },
      { word: "いつも", reading: "itsumo", english: "Always", category: "Time", difficulty: "Intermediate", example: "いつも忙しいです。", exampleTranslation: "I'm always busy." },
      { word: "ときどき", reading: "tokidoki", english: "Sometimes", category: "Time", difficulty: "Intermediate", example: "時々映画を見ます。", exampleTranslation: "I sometimes watch movies." },

      // Animals (20 words)
      { word: "いぬ", reading: "inu", english: "Dog", category: "Animals", difficulty: "Beginner", example: "犬がかわいいです。", exampleTranslation: "The dog is cute." },
      { word: "ねこ", reading: "neko", english: "Cat", category: "Animals", difficulty: "Beginner", example: "猫が寝ています。", exampleTranslation: "The cat is sleeping." },
      { word: "とり", reading: "tori", english: "Bird", category: "Animals", difficulty: "Beginner", example: "鳥が飛んでいます。", exampleTranslation: "The bird is flying." },
      { word: "うま", reading: "uma", english: "Horse", category: "Animals", difficulty: "Beginner", example: "馬が走っています。", exampleTranslation: "The horse is running." },
      { word: "ぞう", reading: "zou", english: "Elephant", category: "Animals", difficulty: "Beginner", example: "象は大きいです。", exampleTranslation: "The elephant is big." },
      { word: "らいおん", reading: "raion", english: "Lion", category: "Animals", difficulty: "Beginner", example: "ライオンは強いです。", exampleTranslation: "The lion is strong." },
      { word: "さる", reading: "saru", english: "Monkey", category: "Animals", difficulty: "Beginner", example: "猿が木に登ります。", exampleTranslation: "The monkey climbs the tree." },
      { word: "うさぎ", reading: "usagi", english: "Rabbit", category: "Animals", difficulty: "Beginner", example: "うさぎが跳んでいます。", exampleTranslation: "The rabbit is jumping." },
      { word: "くま", reading: "kuma", english: "Bear", category: "Animals", difficulty: "Beginner", example: "熊は森にいます。", exampleTranslation: "The bear is in the forest." },
      { word: "きつね", reading: "kitsune", english: "Fox", category: "Animals", difficulty: "Intermediate", example: "狐は賢いです。", exampleTranslation: "The fox is clever." },
      { word: "おおかみ", reading: "ookami", english: "Wolf", category: "Animals", difficulty: "Intermediate", example: "狼が遠吠えします。", exampleTranslation: "The wolf howls." },
      { word: "ぶた", reading: "buta", english: "Pig", category: "Animals", difficulty: "Beginner", example: "豚がかわいいです。", exampleTranslation: "The pig is cute." },
      { word: "ひつじ", reading: "hitsuji", english: "Sheep", category: "Animals", difficulty: "Intermediate", example: "羊の毛は白いです。", exampleTranslation: "The sheep's wool is white." },
      { word: "やぎ", reading: "yagi", english: "Goat", category: "Animals", difficulty: "Intermediate", example: "山羊が草を食べます。", exampleTranslation: "The goat eats grass." },
      { word: "にわとり", reading: "niwatori", english: "Chicken", category: "Animals", difficulty: "Intermediate", example: "鶏が卵を産みます。", exampleTranslation: "The chicken lays eggs." },
      { word: "あひる", reading: "ahiru", english: "Duck", category: "Animals", difficulty: "Intermediate", example: "あひるが泳いでいます。", exampleTranslation: "The duck is swimming." },
      { word: "かめ", reading: "kame", english: "Turtle", category: "Animals", difficulty: "Intermediate", example: "亀はゆっくり歩きます。", exampleTranslation: "The turtle walks slowly." },
      { word: "へび", reading: "hebi", english: "Snake", category: "Animals", difficulty: "Intermediate", example: "蛇が長いです。", exampleTranslation: "The snake is long." },
      { word: "かえる", reading: "kaeru", english: "Frog", category: "Animals", difficulty: "Intermediate", example: "カエルが鳴いています。", exampleTranslation: "The frog is croaking." },
      { word: "むし", reading: "mushi", english: "Insect", category: "Animals", difficulty: "Beginner", example: "虫が飛んでいます。", exampleTranslation: "The insect is flying." },

      // Body Parts (20 words)
      { word: "あたま", reading: "atama", english: "Head", category: "Body", difficulty: "Beginner", example: "頭が痛いです。", exampleTranslation: "My head hurts." },
      { word: "かお", reading: "kao", english: "Face", category: "Body", difficulty: "Beginner", example: "顔を洗います。", exampleTranslation: "I wash my face." },
      { word: "め", reading: "me", english: "Eye", category: "Body", difficulty: "Beginner", example: "目がきれいです。", exampleTranslation: "The eyes are beautiful." },
      { word: "はな", reading: "hana", english: "Nose", category: "Body", difficulty: "Beginner", example: "鼻が高いです。", exampleTranslation: "The nose is high." },
      { word: "くち", reading: "kuchi", english: "Mouth", category: "Body", difficulty: "Beginner", example: "口を開けてください。", exampleTranslation: "Please open your mouth." },
      { word: "みみ", reading: "mimi", english: "Ear", category: "Body", difficulty: "Beginner", example: "耳が聞こえません。", exampleTranslation: "I can't hear." },
      { word: "は", reading: "ha", english: "Tooth", category: "Body", difficulty: "Beginner", example: "歯を磨きます。", exampleTranslation: "I brush my teeth." },
      { word: "くび", reading: "kubi", english: "Neck", category: "Body", difficulty: "Beginner", example: "首が痛いです。", exampleTranslation: "My neck hurts." },
      { word: "かた", reading: "kata", english: "Shoulder", category: "Body", difficulty: "Intermediate", example: "肩がこっています。", exampleTranslation: "My shoulders are stiff." },
      { word: "うで", reading: "ude", english: "Arm", category: "Body", difficulty: "Beginner", example: "腕が強いです。", exampleTranslation: "The arms are strong." },
      { word: "て", reading: "te", english: "Hand", category: "Body", difficulty: "Beginner", example: "手を洗います。", exampleTranslation: "I wash my hands." },
      { word: "ゆび", reading: "yubi", english: "Finger", category: "Body", difficulty: "Beginner", example: "指が長いです。", exampleTranslation: "The fingers are long." },
      { word: "むね", reading: "mune", english: "Chest", category: "Body", difficulty: "Intermediate", example: "胸が痛いです。", exampleTranslation: "My chest hurts." },
      { word: "おなか", reading: "onaka", english: "Stomach", category: "Body", difficulty: "Beginner", example: "お腹がすいています。", exampleTranslation: "I'm hungry." },
      { word: "せなか", reading: "senaka", english: "Back", category: "Body", difficulty: "Intermediate", example: "背中が痛いです。", exampleTranslation: "My back hurts." },
      { word: "あし", reading: "ashi", english: "Leg/Foot", category: "Body", difficulty: "Beginner", example: "足が疲れました。", exampleTranslation: "My legs are tired." },
      { word: "ひざ", reading: "hiza", english: "Knee", category: "Body", difficulty: "Intermediate", example: "膝が痛いです。", exampleTranslation: "My knee hurts." },
      { word: "かみ", reading: "kami", english: "Hair", category: "Body", difficulty: "Beginner", example: "髪が長いです。", exampleTranslation: "The hair is long." },
      { word: "ひふ", reading: "hifu", english: "Skin", category: "Body", difficulty: "Advanced", example: "皮膚がきれいです。", exampleTranslation: "The skin is beautiful." },
      { word: "こころ", reading: "kokoro", english: "Heart", category: "Body", difficulty: "Intermediate", example: "心が温かいです。", exampleTranslation: "The heart is warm." },

      // Clothing (20 words)
      { word: "ふく", reading: "fuku", english: "Clothes", category: "Clothing", difficulty: "Beginner", example: "服を着ます。", exampleTranslation: "I wear clothes." },
      { word: "シャツ", reading: "shatsu", english: "Shirt", category: "Clothing", difficulty: "Beginner", example: "白いシャツを着ています。", exampleTranslation: "I'm wearing a white shirt." },
      { word: "ズボン", reading: "zubon", english: "Pants", category: "Clothing", difficulty: "Beginner", example: "黒いズボンをはいています。", exampleTranslation: "I'm wearing black pants." },
      { word: "スカート", reading: "sukaato", english: "Skirt", category: "Clothing", difficulty: "Beginner", example: "きれいなスカートです。", exampleTranslation: "It's a beautiful skirt." },
      { word: "ドレス", reading: "doresu", english: "Dress", category: "Clothing", difficulty: "Beginner", example: "赤いドレスを着ています。", exampleTranslation: "I'm wearing a red dress." },
      { word: "コート", reading: "kooto", english: "Coat", category: "Clothing", difficulty: "Beginner", example: "冬はコートを着ます。", exampleTranslation: "I wear a coat in winter." },
      { word: "ジャケット", reading: "jaketto", english: "Jacket", category: "Clothing", difficulty: "Beginner", example: "ジャケットを脱ぎます。", exampleTranslation: "I take off my jacket." },
      { word: "セーター", reading: "seetaa", english: "Sweater", category: "Clothing", difficulty: "Beginner", example: "暖かいセーターです。", exampleTranslation: "It's a warm sweater." },
      { word: "くつ", reading: "kutsu", english: "Shoes", category: "Clothing", difficulty: "Beginner", example: "新しい靴を買いました。", exampleTranslation: "I bought new shoes." },
      { word: "くつした", reading: "kutsushita", english: "Socks", category: "Clothing", difficulty: "Beginner", example: "靴下をはきます。", exampleTranslation: "I put on socks." },
      { word: "ぼうし", reading: "boushi", english: "Hat", category: "Clothing", difficulty: "Beginner", example: "帽子をかぶります。", exampleTranslation: "I wear a hat." },
      { word: "てぶくろ", reading: "tebukuro", english: "Gloves", category: "Clothing", difficulty: "Intermediate", example: "手袋をはめます。", exampleTranslation: "I put on gloves." },
      { word: "マフラー", reading: "mafuraa", english: "Scarf", category: "Clothing", difficulty: "Intermediate", example: "マフラーを巻きます。", exampleTranslation: "I wrap a scarf." },
      { word: "ベルト", reading: "beruto", english: "Belt", category: "Clothing", difficulty: "Intermediate", example: "ベルトを締めます。", exampleTranslation: "I fasten my belt." },
      { word: "ネクタイ", reading: "nekutai", english: "Necktie", category: "Clothing", difficulty: "Intermediate", example: "ネクタイを結びます。", exampleTranslation: "I tie my necktie." },
      { word: "めがね", reading: "megane", english: "Glasses", category: "Clothing", difficulty: "Beginner", example: "眼鏡をかけています。", exampleTranslation: "I'm wearing glasses." },
      { word: "とけい", reading: "tokei", english: "Watch", category: "Clothing", difficulty: "Beginner", example: "時計をしています。", exampleTranslation: "I'm wearing a watch." },
      { word: "ゆびわ", reading: "yubiwa", english: "Ring", category: "Clothing", difficulty: "Intermediate", example: "指輪をはめています。", exampleTranslation: "I'm wearing a ring." },
      { word: "イヤリング", reading: "iyaringu", english: "Earrings", category: "Clothing", difficulty: "Intermediate", example: "イヤリングをつけています。", exampleTranslation: "I'm wearing earrings." },
      { word: "かばん", reading: "kaban", english: "Bag", category: "Clothing", difficulty: "Beginner", example: "かばんを持っています。", exampleTranslation: "I'm carrying a bag." },

      // Weather (20 words)
      { word: "てんき", reading: "tenki", english: "Weather", category: "Weather", difficulty: "Beginner", example: "今日はいい天気です。", exampleTranslation: "The weather is nice today." },
      { word: "はれ", reading: "hare", english: "Sunny", category: "Weather", difficulty: "Beginner", example: "晴れの日が好きです。", exampleTranslation: "I like sunny days." },
      { word: "くもり", reading: "kumori", english: "Cloudy", category: "Weather", difficulty: "Beginner", example: "今日は曇りです。", exampleTranslation: "It's cloudy today." },
      { word: "あめ", reading: "ame", english: "Rain", category: "Weather", difficulty: "Beginner", example: "雨が降っています。", exampleTranslation: "It's raining." },
      { word: "ゆき", reading: "yuki", english: "Snow", category: "Weather", difficulty: "Beginner", example: "雪が降りました。", exampleTranslation: "It snowed." },
      { word: "かぜ", reading: "kaze", english: "Wind", category: "Weather", difficulty: "Beginner", example: "風が強いです。", exampleTranslation: "The wind is strong." },
      { word: "あらし", reading: "arashi", english: "Storm", category: "Weather", difficulty: "Intermediate", example: "嵐が来ています。", exampleTranslation: "A storm is coming." },
      { word: "かみなり", reading: "kaminari", english: "Thunder", category: "Weather", difficulty: "Intermediate", example: "雷が鳴っています。", exampleTranslation: "Thunder is rumbling." },
      { word: "きり", reading: "kiri", english: "Fog", category: "Weather", difficulty: "Intermediate", example: "霧が濃いです。", exampleTranslation: "The fog is thick." },
      { word: "あつい", reading: "atsui", english: "Hot", category: "Weather", difficulty: "Beginner", example: "今日は暑いです。", exampleTranslation: "It's hot today." },
      { word: "さむい", reading: "samui", english: "Cold", category: "Weather", difficulty: "Beginner", example: "冬は寒いです。", exampleTranslation: "Winter is cold." },
      { word: "すずしい", reading: "suzushii", english: "Cool", category: "Weather", difficulty: "Beginner", example: "秋は涼しいです。", exampleTranslation: "Autumn is cool." },
      { word: "あたたかい", reading: "atatakai", english: "Warm", category: "Weather", difficulty: "Beginner", example: "春は暖かいです。", exampleTranslation: "Spring is warm." },
      { word: "しつど", reading: "shitsudo", english: "Humidity", category: "Weather", difficulty: "Advanced", example: "湿度が高いです。", exampleTranslation: "The humidity is high." },
      { word: "きおん", reading: "kion", english: "Temperature", category: "Weather", difficulty: "Intermediate", example: "気温が上がりました。", exampleTranslation: "The temperature rose." },
      { word: "たいふう", reading: "taifuu", english: "Typhoon", category: "Weather", difficulty: "Intermediate", example: "台風が近づいています。", exampleTranslation: "A typhoon is approaching." },
      { word: "にじ", reading: "niji", english: "Rainbow", category: "Weather", difficulty: "Intermediate", example: "虹が見えます。", exampleTranslation: "I can see a rainbow." },
      { word: "つゆ", reading: "tsuyu", english: "Rainy season", category: "Weather", difficulty: "Advanced", example: "梅雨の季節です。", exampleTranslation: "It's the rainy season." },
      { word: "ひょう", reading: "hyou", english: "Hail", category: "Weather", difficulty: "Advanced", example: "雹が降りました。", exampleTranslation: "It hailed." },
      { word: "かんそう", reading: "kansou", english: "Dry", category: "Weather", difficulty: "Intermediate", example: "空気が乾燥しています。", exampleTranslation: "The air is dry." },

      // Transportation (20 words)
      { word: "でんしゃ", reading: "densha", english: "Train", category: "Transportation", difficulty: "Beginner", example: "電車で行きます。", exampleTranslation: "I go by train." },
      { word: "バス", reading: "basu", english: "Bus", category: "Transportation", difficulty: "Beginner", example: "バスに乗ります。", exampleTranslation: "I ride the bus." },
      { word: "タクシー", reading: "takushii", english: "Taxi", category: "Transportation", difficulty: "Beginner", example: "タクシーを呼びます。", exampleTranslation: "I call a taxi." },
      { word: "ひこうき", reading: "hikouki", english: "Airplane", category: "Transportation", difficulty: "Beginner", example: "飛行機で旅行します。", exampleTranslation: "I travel by airplane." },
      { word: "ふね", reading: "fune", english: "Ship", category: "Transportation", difficulty: "Beginner", example: "船で島に行きます。", exampleTranslation: "I go to the island by ship." },
      { word: "ちかてつ", reading: "chikatetsu", english: "Subway", category: "Transportation", difficulty: "Intermediate", example: "地下鉄が便利です。", exampleTranslation: "The subway is convenient." },
      { word: "しんかんせん", reading: "shinkansen", english: "Bullet train", category: "Transportation", difficulty: "Intermediate", example: "新幹線は速いです。", exampleTranslation: "The bullet train is fast." },
      { word: "オートバイ", reading: "ootobai", english: "Motorcycle", category: "Transportation", difficulty: "Intermediate", example: "オートバイに乗ります。", exampleTranslation: "I ride a motorcycle." },
      { word: "トラック", reading: "torakku", english: "Truck", category: "Transportation", difficulty: "Intermediate", example: "トラックが荷物を運びます。", exampleTranslation: "The truck carries cargo." },
      { word: "きゅうきゅうしゃ", reading: "kyuukyuusha", english: "Ambulance", category: "Transportation", difficulty: "Advanced", example: "救急車が来ました。", exampleTranslation: "The ambulance came." },
      { word: "しょうぼうしゃ", reading: "shoubousha", english: "Fire truck", category: "Transportation", difficulty: "Advanced", example: "消防車が急いでいます。", exampleTranslation: "The fire truck is rushing." },
      { word: "パトカー", reading: "patokaа", english: "Police car", category: "Transportation", difficulty: "Intermediate", example: "パトカーが通りました。", exampleTranslation: "A police car passed by." },
      { word: "ヘリコプター", reading: "herikoputaa", english: "Helicopter", category: "Transportation", difficulty: "Intermediate", example: "ヘリコプターが飛んでいます。", exampleTranslation: "A helicopter is flying." },
      { word: "ボート", reading: "booto", english: "Boat", category: "Transportation", difficulty: "Beginner", example: "ボートで釣りをします。", exampleTranslation: "I fish from a boat." },
      { word: "ヨット", reading: "yotto", english: "Yacht", category: "Transportation", difficulty: "Intermediate", example: "ヨットでセーリングします。", exampleTranslation: "I sail on a yacht." },
      { word: "ロケット", reading: "roketto", english: "Rocket", category: "Transportation", difficulty: "Advanced", example: "ロケットが宇宙に飛びます。", exampleTranslation: "The rocket flies to space." },
      { word: "エレベーター", reading: "erebeetaa", english: "Elevator", category: "Transportation", difficulty: "Intermediate", example: "エレベーターで上がります。", exampleTranslation: "I go up by elevator." },
      { word: "エスカレーター", reading: "esukareetaa", english: "Escalator", category: "Transportation", difficulty: "Intermediate", example: "エスカレーターを使います。", exampleTranslation: "I use the escalator." },
      { word: "どうろ", reading: "douro", english: "Road", category: "Transportation", difficulty: "Beginner", example: "道路を渡ります。", exampleTranslation: "I cross the road." },
      { word: "きょうり", reading: "kyouri", english: "Distance", category: "Transportation", difficulty: "Advanced", example: "距離が遠いです。", exampleTranslation: "The distance is far." },

      // School (20 words)
      { word: "がっこう", reading: "gakkou", english: "School", category: "School", difficulty: "Beginner", example: "学校に行きます。", exampleTranslation: "I go to school." },
      { word: "せんせい", reading: "sensei", english: "Teacher", category: "School", difficulty: "Beginner", example: "先生が教えます。", exampleTranslation: "The teacher teaches." },
      { word: "せいと", reading: "seito", english: "Student", category: "School", difficulty: "Beginner", example: "生徒が勉強します。", exampleTranslation: "The student studies." },
      { word: "きょうしつ", reading: "kyoushitsu", english: "Classroom", category: "School", difficulty: "Beginner", example: "教室で授業があります。", exampleTranslation: "There's a class in the classroom." },
      { word: "じゅぎょう", reading: "jugyou", english: "Class/Lesson", category: "School", difficulty: "Beginner", example: "授業が始まります。", exampleTranslation: "Class begins." },
      { word: "しゅくだい", reading: "shukudai", english: "Homework", category: "School", difficulty: "Beginner", example: "宿題をします。", exampleTranslation: "I do homework." },
      { word: "しけん", reading: "shiken", english: "Exam", category: "School", difficulty: "Beginner", example: "試験があります。", exampleTranslation: "There's an exam." },
      { word: "きょうかしょ", reading: "kyoukasho", english: "Textbook", category: "School", difficulty: "Intermediate", example: "教科書を読みます。", exampleTranslation: "I read the textbook." },
      { word: "ノート", reading: "nooto", english: "Notebook", category: "School", difficulty: "Beginner", example: "ノートに書きます。", exampleTranslation: "I write in my notebook." },
      { word: "えんぴつ", reading: "enpitsu", english: "Pencil", category: "School", difficulty: "Beginner", example: "鉛筆で書きます。", exampleTranslation: "I write with a pencil." },
      { word: "けしゴム", reading: "keshigomu", english: "Eraser", category: "School", difficulty: "Beginner", example: "消しゴムで消します。", exampleTranslation: "I erase with an eraser." },
      { word: "じょうぎ", reading: "jougi", english: "Ruler", category: "School", difficulty: "Intermediate", example: "定規で線を引きます。", exampleTranslation: "I draw a line with a ruler." },
      { word: "こくばん", reading: "kokuban", english: "Blackboard", category: "School", difficulty: "Intermediate", example: "黒板に書きます。", exampleTranslation: "I write on the blackboard." },
      { word: "チョーク", reading: "chooku", english: "Chalk", category: "School", difficulty: "Intermediate", example: "チョークで書きます。", exampleTranslation: "I write with chalk." },
      { word: "としょかん", reading: "toshokan", english: "Library", category: "School", difficulty: "Intermediate", example: "図書館で本を借ります。", exampleTranslation: "I borrow books from the library." },
      { word: "たいいくかん", reading: "taiikukan", english: "Gymnasium", category: "School", difficulty: "Advanced", example: "体育館で運動します。", exampleTranslation: "I exercise in the gymnasium." },
      { word: "りかしつ", reading: "rikashitsu", english: "Science lab", category: "School", difficulty: "Advanced", example: "理科室で実験します。", exampleTranslation: "I do experiments in the science lab." },
      { word: "おんがくしつ", reading: "ongakushitsu", english: "Music room", category: "School", difficulty: "Advanced", example: "音楽室で歌います。", exampleTranslation: "I sing in the music room." },
      { word: "しょくどう", reading: "shokudou", english: "Cafeteria", category: "School", difficulty: "Intermediate", example: "食堂で昼食を食べます。", exampleTranslation: "I eat lunch in the cafeteria." },
      { word: "うんどうじょう", reading: "undoujou", english: "Playground", category: "School", difficulty: "Advanced", example: "運動場で遊びます。", exampleTranslation: "I play on the playground." },

      // Work (20 words)
      { word: "しごと", reading: "shigoto", english: "Work/Job", category: "Work", difficulty: "Beginner", example: "仕事をします。", exampleTranslation: "I work." },
      { word: "かいしゃ", reading: "kaisha", english: "Company", category: "Work", difficulty: "Beginner", example: "会社で働きます。", exampleTranslation: "I work at a company." },
      { word: "しゃちょう", reading: "shachou", english: "Company president", category: "Work", difficulty: "Intermediate", example: "社長が来ました。", exampleTranslation: "The company president came." },
      { word: "ぶちょう", reading: "buchou", english: "Department manager", category: "Work", difficulty: "Advanced", example: "部長と会議します。", exampleTranslation: "I have a meeting with the department manager." },
      { word: "どうりょう", reading: "douryou", english: "Colleague", category: "Work", difficulty: "Intermediate", example: "同僚と働きます。", exampleTranslation: "I work with colleagues." },
      { word: "きゅうりょう", reading: "kyuuryou", english: "Salary", category: "Work", difficulty: "Intermediate", example: "給料をもらいます。", exampleTranslation: "I receive my salary." },
      { word: "かいぎ", reading: "kaigi", english: "Meeting", category: "Work", difficulty: "Intermediate", example: "会議があります。", exampleTranslation: "There's a meeting." },
      { word: "プロジェクト", reading: "purojekuto", english: "Project", category: "Work", difficulty: "Intermediate", example: "プロジェクトを進めます。", exampleTranslation: "I advance the project." },
      { word: "しめきり", reading: "shimekiri", english: "Deadline", category: "Work", difficulty: "Advanced", example: "締切が近いです。", exampleTranslation: "The deadline is near." },
      { word: "ざんぎょう", reading: "zangyou", english: "Overtime", category: "Work", difficulty: "Advanced", example: "残業をします。", exampleTranslation: "I work overtime." },
      { word: "きゅうか", reading: "kyuuka", english: "Vacation", category: "Work", difficulty: "Intermediate", example: "休暇を取ります。", exampleTranslation: "I take a vacation." },
      { word: "しゅっちょう", reading: "shutchou", english: "Business trip", category: "Work", difficulty: "Advanced", example: "出張に行きます。", exampleTranslation: "I go on a business trip." },
      { word: "めんせつ", reading: "mensetsu", english: "Interview", category: "Work", difficulty: "Advanced", example: "面接を受けます。", exampleTranslation: "I have an interview." },
      { word: "りれきしょ", reading: "rirekisho", english: "Resume", category: "Work", difficulty: "Advanced", example: "履歴書を書きます。", exampleTranslation: "I write a resume." },
      { word: "けいやく", reading: "keiyaku", english: "Contract", category: "Work", difficulty: "Advanced", example: "契約を結びます。", exampleTranslation: "I sign a contract." },
      { word: "ぎじゅつ", reading: "gijutsu", english: "Technology/Skill", category: "Work", difficulty: "Advanced", example: "技術を学びます。", exampleTranslation: "I learn technology." },
      { word: "けいけん", reading: "keiken", english: "Experience", category: "Work", difficulty: "Intermediate", example: "経験があります。", exampleTranslation: "I have experience." },
      { word: "のうりょく", reading: "nouryoku", english: "Ability", category: "Work", difficulty: "Advanced", example: "能力を向上させます。", exampleTranslation: "I improve my abilities." },
      { word: "せきにん", reading: "sekinin", english: "Responsibility", category: "Work", difficulty: "Advanced", example: "責任があります。", exampleTranslation: "I have responsibility." },
      { word: "せいか", reading: "seika", english: "Results", category: "Work", difficulty: "Advanced", example: "成果を出します。", exampleTranslation: "I produce results." },

      // Sports (20 words)
      { word: "スポーツ", reading: "supootsu", english: "Sports", category: "Sports", difficulty: "Beginner", example: "スポーツが好きです。", exampleTranslation: "I like sports." },
      { word: "やきゅう", reading: "yakyuu", english: "Baseball", category: "Sports", difficulty: "Beginner", example: "野球をします。", exampleTranslation: "I play baseball." },
      { word: "サッカー", reading: "sakkaa", english: "Soccer", category: "Sports", difficulty: "Beginner", example: "サッカーを見ます。", exampleTranslation: "I watch soccer." },
      { word: "バスケットボール", reading: "basukettobooru", english: "Basketball", category: "Sports", difficulty: "Beginner", example: "バスケットボールをします。", exampleTranslation: "I play basketball." },
      { word: "テニス", reading: "tenisu", english: "Tennis", category: "Sports", difficulty: "Beginner", example: "テニスが上手です。", exampleTranslation: "I'm good at tennis." },
      { word: "すいえい", reading: "suiei", english: "Swimming", category: "Sports", difficulty: "Beginner", example: "水泳を習います。", exampleTranslation: "I learn swimming." },
      { word: "マラソン", reading: "marason", english: "Marathon", category: "Sports", difficulty: "Intermediate", example: "マラソンに参加します。", exampleTranslation: "I participate in a marathon." },
      { word: "ゴルフ", reading: "gorufu", english: "Golf", category: "Sports", difficulty: "Intermediate", example: "ゴルフを練習します。", exampleTranslation: "I practice golf." },
      { word: "スキー", reading: "sukii", english: "Skiing", category: "Sports", difficulty: "Intermediate", example: "スキーに行きます。", exampleTranslation: "I go skiing." },
      { word: "スケート", reading: "sukeeto", english: "Skating", category: "Sports", difficulty: "Intermediate", example: "スケートができます。", exampleTranslation: "I can skate." },
      { word: "じゅうどう", reading: "juudou", english: "Judo", category: "Sports", difficulty: "Intermediate", example: "柔道を習います。", exampleTranslation: "I learn judo." },
      { word: "からて", reading: "karate", english: "Karate", category: "Sports", difficulty: "Intermediate", example: "空手が強いです。", exampleTranslation: "I'm strong at karate." },
      { word: "ヨガ", reading: "yoga", english: "Yoga", category: "Sports", difficulty: "Beginner", example: "ヨガをします。", exampleTranslation: "I do yoga." },
      { word: "ジョギング", reading: "jogingu", english: "Jogging", category: "Sports", difficulty: "Beginner", example: "毎朝ジョギングします。", exampleTranslation: "I jog every morning." },
      { word: "サイクリング", reading: "saikuringu", english: "Cycling", category: "Sports", difficulty: "Intermediate", example: "サイクリングが好きです。", exampleTranslation: "I like cycling." },
      { word: "ハイキング", reading: "haikingu", english: "Hiking", category: "Sports", difficulty: "Intermediate", example: "山でハイキングします。", exampleTranslation: "I hike in the mountains." },
      { word: "つり", reading: "tsuri", english: "Fishing", category: "Sports", difficulty: "Beginner", example: "釣りに行きます。", exampleTranslation: "I go fishing." },
      { word: "ボーリング", reading: "booringu", english: "Bowling", category: "Sports", difficulty: "Beginner", example: "ボーリングをします。", exampleTranslation: "I go bowling." },
      { word: "たっきゅう", reading: "takkyuu", english: "Table tennis", category: "Sports", difficulty: "Intermediate", example: "卓球が得意です。", exampleTranslation: "I'm good at table tennis." },
      { word: "バレーボール", reading: "bareebooru", english: "Volleyball", category: "Sports", difficulty: "Intermediate", example: "バレーボールをします。", exampleTranslation: "I play volleyball." },

      // Music (20 words)
      { word: "おんがく", reading: "ongaku", english: "Music", category: "Music", difficulty: "Beginner", example: "音楽を聞きます。", exampleTranslation: "I listen to music." },
      { word: "うた", reading: "uta", english: "Song", category: "Music", difficulty: "Beginner", example: "歌を歌います。", exampleTranslation: "I sing a song." },
      { word: "ピアノ", reading: "piano", english: "Piano", category: "Music", difficulty: "Beginner", example: "ピアノを弾きます。", exampleTranslation: "I play the piano." },
      { word: "ギター", reading: "gitaa", english: "Guitar", category: "Music", difficulty: "Beginner", example: "ギターを習います。", exampleTranslation: "I learn guitar." },
      { word: "バイオリン", reading: "baiorin", english: "Violin", category: "Music", difficulty: "Intermediate", example: "バイオリンが美しいです。", exampleTranslation: "The violin is beautiful." },
      { word: "ドラム", reading: "doramu", english: "Drums", category: "Music", difficulty: "Beginner", example: "ドラムを叩きます。", exampleTranslation: "I play the drums." },
      { word: "フルート", reading: "furuuto", english: "Flute", category: "Music", difficulty: "Intermediate", example: "フルートを吹きます。", exampleTranslation: "I play the flute." },
      { word: "トランペット", reading: "toranpetto", english: "Trumpet", category: "Music", difficulty: "Intermediate", example: "トランペットが響きます。", exampleTranslation: "The trumpet resonates." },
      { word: "サクソフォン", reading: "sakusofon", english: "Saxophone", category: "Music", difficulty: "Advanced", example: "サクソフォンを演奏します。", exampleTranslation: "I perform on the saxophone." },
      { word: "コンサート", reading: "konsaato", english: "Concert", category: "Music", difficulty: "Beginner", example: "コンサートに行きます。", exampleTranslation: "I go to a concert." },
      { word: "オーケストラ", reading: "ookesutora", english: "Orchestra", category: "Music", difficulty: "Advanced", example: "オーケストラを聞きます。", exampleTranslation: "I listen to the orchestra." },
      { word: "バンド", reading: "bando", english: "Band", category: "Music", difficulty: "Beginner", example: "バンドを組みます。", exampleTranslation: "I form a band." },
      { word: "メロディー", reading: "merodhii", english: "Melody", category: "Music", difficulty: "Intermediate", example: "メロディーがきれいです。", exampleTranslation: "The melody is beautiful." },
      { word: "リズム", reading: "rizumu", english: "Rhythm", category: "Music", difficulty: "Intermediate", example: "リズムに合わせます。", exampleTranslation: "I match the rhythm." },
      { word: "ハーモニー", reading: "haamonii", english: "Harmony", category: "Music", difficulty: "Advanced", example: "ハーモニーが美しいです。", exampleTranslation: "The harmony is beautiful." },
      { word: "がっき", reading: "gakki", english: "Musical instrument", category: "Music", difficulty: "Intermediate", example: "楽器を演奏します。", exampleTranslation: "I play a musical instrument." },
      { word: "れんしゅう", reading: "renshuu", english: "Practice", category: "Music", difficulty: "Beginner", example: "毎日練習します。", exampleTranslation: "I practice every day." },
      { word: "はっぴょう", reading: "happyou", english: "Performance", category: "Music", difficulty: "Advanced", example: "発表会があります。", exampleTranslation: "There's a performance." },
      { word: "ろっく", reading: "rokku", english: "Rock music", category: "Music", difficulty: "Beginner", example: "ロック音楽が好きです。", exampleTranslation: "I like rock music." },
      { word: "クラシック", reading: "kurashikku", english: "Classical music", category: "Music", difficulty: "Intermediate", example: "クラシック音楽を聞きます。", exampleTranslation: "I listen to classical music." },

      // Technology (20 words)
      { word: "コンピューター", reading: "konpyuutaa", english: "Computer", category: "Technology", difficulty: "Beginner", example: "コンピューターを使います。", exampleTranslation: "I use a computer." },
      { word: "インターネット", reading: "intaanetto", english: "Internet", category: "Technology", difficulty: "Beginner", example: "インターネットで調べます。", exampleTranslation: "I search on the internet." },
      { word: "スマートフォン", reading: "sumaatofon", english: "Smartphone", category: "Technology", difficulty: "Beginner", example: "スマートフォンを使います。", exampleTranslation: "I use a smartphone." },
      { word: "タブレット", reading: "taburetto", english: "Tablet", category: "Technology", difficulty: "Beginner", example: "タブレットで読みます。", exampleTranslation: "I read on a tablet." },
      { word: "ソフトウェア", reading: "sofutowea", english: "Software", category: "Technology", difficulty: "Advanced", example: "ソフトウェアを更新します。", exampleTranslation: "I update the software." },
      { word: "アプリ", reading: "apuri", english: "App", category: "Technology", difficulty: "Beginner", example: "アプリをダウンロードします。", exampleTranslation: "I download an app." },
      { word: "ウェブサイト", reading: "uebusaito", english: "Website", category: "Technology", difficulty: "Intermediate", example: "ウェブサイトを見ます。", exampleTranslation: "I look at a website." },
      { word: "メール", reading: "meeru", english: "Email", category: "Technology", difficulty: "Beginner", example: "メールを送ります。", exampleTranslation: "I send an email." },
      { word: "パスワード", reading: "pasuwaado", english: "Password", category: "Technology", difficulty: "Intermediate", example: "パスワードを入力します。", exampleTranslation: "I enter the password." },
      { word: "データ", reading: "deeta", english: "Data", category: "Technology", difficulty: "Intermediate", example: "データを保存します。", exampleTranslation: "I save the data." },
      { word: "ファイル", reading: "fairu", english: "File", category: "Technology", difficulty: "Intermediate", example: "ファイルを開きます。", exampleTranslation: "I open the file." },
      { word: "プリンター", reading: "purintaa", english: "Printer", category: "Technology", difficulty: "Beginner", example: "プリンターで印刷します。", exampleTranslation: "I print with the printer." },
      { word: "スキャナー", reading: "sukyanaa", english: "Scanner", category: "Technology", difficulty: "Intermediate", example: "スキャナーで読み取ります。", exampleTranslation: "I scan with the scanner." },
      { word: "カメラ", reading: "kamera", english: "Camera", category: "Technology", difficulty: "Beginner", example: "カメラで写真を撮ります。", exampleTranslation: "I take photos with a camera." },
      { word: "ビデオ", reading: "bideo", english: "Video", category: "Technology", difficulty: "Beginner", example: "ビデオを見ます。", exampleTranslation: "I watch a video." },
      { word: "ゲーム", reading: "geemu", english: "Game", category: "Technology", difficulty: "Beginner", example: "ゲームをします。", exampleTranslation: "I play a game." },
      { word: "ロボット", reading: "robotto", english: "Robot", category: "Technology", difficulty: "Intermediate", example: "ロボットが働きます。", exampleTranslation: "The robot works." },
      { word: "AI", reading: "eiai", english: "Artificial Intelligence", category: "Technology", difficulty: "Advanced", example: "AIが発達しています。", exampleTranslation: "AI is developing." },
      { word: "バーチャル", reading: "baachuaru", english: "Virtual", category: "Technology", difficulty: "Advanced", example: "バーチャル会議をします。", exampleTranslation: "I have a virtual meeting." },
      { word: "クラウド", reading: "kuraudo", english: "Cloud", category: "Technology", difficulty: "Advanced", example: "クラウドに保存します。", exampleTranslation: "I save to the cloud." },

      // Nature (20 words)
      { word: "しぜん", reading: "shizen", english: "Nature", category: "Nature", difficulty: "Beginner", example: "自然が美しいです。", exampleTranslation: "Nature is beautiful." },
      { word: "やま", reading: "yama", english: "Mountain", category: "Nature", difficulty: "Beginner", example: "山に登ります。", exampleTranslation: "I climb the mountain." },
      { word: "うみ", reading: "umi", english: "Sea/Ocean", category: "Nature", difficulty: "Beginner", example: "海で泳ぎます。", exampleTranslation: "I swim in the sea." },
      { word: "かわ", reading: "kawa", english: "River", category: "Nature", difficulty: "Beginner", example: "川で釣りをします。", exampleTranslation: "I fish in the river." },
      { word: "みずうみ", reading: "mizuumi", english: "Lake", category: "Nature", difficulty: "Intermediate", example: "湖がきれいです。", exampleTranslation: "The lake is beautiful." },
      { word: "もり", reading: "mori", english: "Forest", category: "Nature", difficulty: "Beginner", example: "森を歩きます。", exampleTranslation: "I walk in the forest." },
      { word: "はな", reading: "hana", english: "Flower", category: "Nature", difficulty: "Beginner", example: "花がきれいです。", exampleTranslation: "The flowers are beautiful." },
      { word: "き", reading: "ki", english: "Tree", category: "Nature", difficulty: "Beginner", example: "木が高いです。", exampleTranslation: "The tree is tall." },
      { word: "くさ", reading: "kusa", english: "Grass", category: "Nature", difficulty: "Beginner", example: "草が緑です。", exampleTranslation: "The grass is green." },
      { word: "はっぱ", reading: "happa", english: "Leaf", category: "Nature", difficulty: "Beginner", example: "葉っぱが落ちます。", exampleTranslation: "The leaves fall." },
      { word: "たいよう", reading: "taiyou", english: "Sun", category: "Nature", difficulty: "Beginner", example: "太陽が明るいです。", exampleTranslation: "The sun is bright." },
      { word: "つき", reading: "tsuki", english: "Moon", category: "Nature", difficulty: "Beginner", example: "月がきれいです。", exampleTranslation: "The moon is beautiful." },
      { word: "ほし", reading: "hoshi", english: "Star", category: "Nature", difficulty: "Beginner", example: "星が光ります。", exampleTranslation: "The stars shine." },
      { word: "そら", reading: "sora", english: "Sky", category: "Nature", difficulty: "Beginner", example: "空が青いです。", exampleTranslation: "The sky is blue." },
      { word: "くも", reading: "kumo", english: "Cloud", category: "Nature", difficulty: "Beginner", example: "雲が白いです。", exampleTranslation: "The clouds are white." },
      { word: "いし", reading: "ishi", english: "Stone/Rock", category: "Nature", difficulty: "Beginner", example: "石を投げます。", exampleTranslation: "I throw a stone." },
      { word: "すな", reading: "suna", english: "Sand", category: "Nature", difficulty: "Beginner", example: "砂で遊びます。", exampleTranslation: "I play with sand." },
      { word: "つち", reading: "tsuchi", english: "Soil/Earth", category: "Nature", difficulty: "Intermediate", example: "土を掘ります。", exampleTranslation: "I dig the soil." },
      { word: "しま", reading: "shima", english: "Island", category: "Nature", difficulty: "Intermediate", example: "島に行きます。", exampleTranslation: "I go to the island." },
      { word: "たに", reading: "tani", english: "Valley", category: "Nature", difficulty: "Intermediate", example: "谷を歩きます。", exampleTranslation: "I walk through the valley." },

      // Emotions (20 words)
      { word: "きもち", reading: "kimochi", english: "Feeling", category: "Emotions", difficulty: "Beginner", example: "気持ちがいいです。", exampleTranslation: "I feel good." },
      { word: "うれしい", reading: "ureshii", english: "Happy", category: "Emotions", difficulty: "Beginner", example: "とても嬉しいです。", exampleTranslation: "I'm very happy." },
      { word: "かなしい", reading: "kanashii", english: "Sad", category: "Emotions", difficulty: "Beginner", example: "悲しい映画です。", exampleTranslation: "It's a sad movie." },
      { word: "おこる", reading: "okoru", english: "Angry", category: "Emotions", difficulty: "Beginner", example: "怒っています。", exampleTranslation: "I'm angry." },
      { word: "こわい", reading: "kowai", english: "Scary/Afraid", category: "Emotions", difficulty: "Beginner", example: "怖い話です。", exampleTranslation: "It's a scary story." },
      { word: "たのしい", reading: "tanoshii", english: "Fun/Enjoyable", category: "Emotions", difficulty: "Beginner", example: "楽しいパーティーです。", exampleTranslation: "It's a fun party." },
      { word: "つまらない", reading: "tsumaranai", english: "Boring", category: "Emotions", difficulty: "Beginner", example: "つまらない映画です。", exampleTranslation: "It's a boring movie." },
      { word: "しんぱい", reading: "shinpai", english: "Worry", category: "Emotions", difficulty: "Intermediate", example: "心配しています。", exampleTranslation: "I'm worried." },
      { word: "あんしん", reading: "anshin", english: "Relief", category: "Emotions", difficulty: "Intermediate", example: "安心しました。", exampleTranslation: "I'm relieved." },
      { word: "びっくり", reading: "bikkuri", english: "Surprised", category: "Emotions", difficulty: "Beginner", example: "びっくりしました。", exampleTranslation: "I was surprised." },
      { word: "がっかり", reading: "gakkari", english: "Disappointed", category: "Emotions", difficulty: "Intermediate", example: "がっかりしました。", exampleTranslation: "I was disappointed." },
      { word: "こうふん", reading: "koufun", english: "Excited", category: "Emotions", difficulty: "Intermediate", example: "興奮しています。", exampleTranslation: "I'm excited." },
      { word: "しずか", reading: "shizuka", english: "Calm/Quiet", category: "Emotions", difficulty: "Beginner", example: "静かな気持ちです。", exampleTranslation: "I feel calm." },
      { word: "ストレス", reading: "sutoresu", english: "Stress", category: "Emotions", difficulty: "Intermediate", example: "ストレスがあります。", exampleTranslation: "I have stress." },
      { word: "リラックス", reading: "rirakkusu", english: "Relax", category: "Emotions", difficulty: "Intermediate", example: "リラックスします。", exampleTranslation: "I relax." },
      { word: "あい", reading: "ai", english: "Love", category: "Emotions", difficulty: "Intermediate", example: "愛しています。", exampleTranslation: "I love you." },
      { word: "きらい", reading: "kirai", english: "Dislike", category: "Emotions", difficulty: "Beginner", example: "野菜が嫌いです。", exampleTranslation: "I dislike vegetables." },
      { word: "すき", reading: "suki", english: "Like", category: "Emotions", difficulty: "Beginner", example: "音楽が好きです。", exampleTranslation: "I like music." },
      { word: "さびしい", reading: "sabishii", english: "Lonely", category: "Emotions", difficulty: "Intermediate", example: "寂しいです。", exampleTranslation: "I'm lonely." },
      { word: "まんぞく", reading: "manzoku", english: "Satisfied", category: "Emotions", difficulty: "Advanced", example: "満足しています。", exampleTranslation: "I'm satisfied." },

      // Actions (20 words)
      { word: "たべる", reading: "taberu", english: "To eat", category: "Actions", difficulty: "Beginner", example: "ご飯を食べます。", exampleTranslation: "I eat rice." },
      { word: "のむ", reading: "nomu", english: "To drink", category: "Actions", difficulty: "Beginner", example: "水を飲みます。", exampleTranslation: "I drink water." },
      { word: "ねる", reading: "neru", english: "To sleep", category: "Actions", difficulty: "Beginner", example: "夜に寝ます。", exampleTranslation: "I sleep at night." },
      { word: "おきる", reading: "okiru", english: "To wake up", category: "Actions", difficulty: "Beginner", example: "朝早く起きます。", exampleTranslation: "I wake up early in the morning." },
      { word: "あるく", reading: "aruku", english: "To walk", category: "Actions", difficulty: "Beginner", example: "公園を歩きます。", exampleTranslation: "I walk in the park." },
      { word: "はしる", reading: "hashiru", english: "To run", category: "Actions", difficulty: "Beginner", example: "毎日走ります。", exampleTranslation: "I run every day." },
      { word: "よむ", reading: "yomu", english: "To read", category: "Actions", difficulty: "Beginner", example: "本を読みます。", exampleTranslation: "I read a book." },
      { word: "かく", reading: "kaku", english: "To write", category: "Actions", difficulty: "Beginner", example: "手紙を書きます。", exampleTranslation: "I write a letter." },
      { word: "きく", reading: "kiku", english: "To listen", category: "Actions", difficulty: "Beginner", example: "音楽を聞きます。", exampleTranslation: "I listen to music." },
      { word: "みる", reading: "miru", english: "To see/watch", category: "Actions", difficulty: "Beginner", example: "テレビを見ます。", exampleTranslation: "I watch TV." },
      { word: "はなす", reading: "hanasu", english: "To speak", category: "Actions", difficulty: "Beginner", example: "日本語を話します。", exampleTranslation: "I speak Japanese." },
      { word: "べんきょう", reading: "benkyou", english: "To study", category: "Actions", difficulty: "Beginner", example: "勉強します。", exampleTranslation: "I study." },
      { word: "はたらく", reading: "hataraku", english: "To work", category: "Actions", difficulty: "Beginner", example: "会社で働きます。", exampleTranslation: "I work at a company." },
      { word: "あそぶ", reading: "asobu", english: "To play", category: "Actions", difficulty: "Beginner", example: "友達と遊びます。", exampleTranslation: "I play with friends." },
      { word: "りょうり", reading: "ryouri", english: "To cook", category: "Actions", difficulty: "Beginner", example: "料理をします。", exampleTranslation: "I cook." },
      { word: "そうじ", reading: "souji", english: "To clean", category: "Actions", difficulty: "Beginner", example: "部屋を掃除します。", exampleTranslation: "I clean the room." },
      { word: "かう", reading: "kau", english: "To buy", category: "Actions", difficulty: "Beginner", example: "食べ物を買います。", exampleTranslation: "I buy food." },
      { word: "うる", reading: "uru", english: "To sell", category: "Actions", difficulty: "Intermediate", example: "車を売ります。", exampleTranslation: "I sell a car." },
      { word: "おしえる", reading: "oshieru", english: "To teach", category: "Actions", difficulty: "Intermediate", example: "日本語を教えます。", exampleTranslation: "I teach Japanese." },
      { word: "ならう", reading: "narau", english: "To learn", category: "Actions", difficulty: "Beginner", example: "ピアノを習います。", exampleTranslation: "I learn piano." }
    ];

    // Character sets for writing practice
    const hiraganaChars = [
      { char: "あ", reading: "a" }, { char: "い", reading: "i" }, { char: "う", reading: "u" }, { char: "え", reading: "e" }, { char: "お", reading: "o" },
      { char: "か", reading: "ka" }, { char: "き", reading: "ki" }, { char: "く", reading: "ku" }, { char: "け", reading: "ke" }, { char: "こ", reading: "ko" },
      { char: "さ", reading: "sa" }, { char: "し", reading: "shi" }, { char: "す", reading: "su" }, { char: "せ", reading: "se" }, { char: "そ", reading: "so" },
      { char: "た", reading: "ta" }, { char: "ち", reading: "chi" }, { char: "つ", reading: "tsu" }, { char: "て", reading: "te" }, { char: "と", reading: "to" },
      { char: "な", reading: "na" }, { char: "に", reading: "ni" }, { char: "ぬ", reading: "nu" }, { char: "ね", reading: "ne" }, { char: "の", reading: "no" }
    ];

    const katakanaChars = [
      { char: "ア", reading: "a" }, { char: "イ", reading: "i" }, { char: "ウ", reading: "u" }, { char: "エ", reading: "e" }, { char: "オ", reading: "o" },
      { char: "カ", reading: "ka" }, { char: "キ", reading: "ki" }, { char: "ク", reading: "ku" }, { char: "ケ", reading: "ke" }, { char: "コ", reading: "ko" },
      { char: "サ", reading: "sa" }, { char: "シ", reading: "shi" }, { char: "ス", reading: "su" }, { char: "セ", reading: "se" }, { char: "ソ", reading: "so" },
      { char: "タ", reading: "ta" }, { char: "チ", reading: "chi" }, { char: "ツ", reading: "tsu" }, { char: "テ", reading: "te" }, { char: "ト", reading: "to" },
      { char: "ナ", reading: "na" }, { char: "ニ", reading: "ni" }, { char: "ヌ", reading: "nu" }, { char: "ネ", reading: "ne" }, { char: "ノ", reading: "no" }
    ];

    const kanjiChars = [
      { char: "一", reading: "ichi", meaning: "one" }, { char: "二", reading: "ni", meaning: "two" }, { char: "三", reading: "san", meaning: "three" },
      { char: "四", reading: "yon", meaning: "four" }, { char: "五", reading: "go", meaning: "five" }, { char: "六", reading: "roku", meaning: "six" },
      { char: "七", reading: "nana", meaning: "seven" }, { char: "八", reading: "hachi", meaning: "eight" }, { char: "九", reading: "kyuu", meaning: "nine" },
      { char: "十", reading: "juu", meaning: "ten" }, { char: "人", reading: "hito", meaning: "person" }, { char: "日", reading: "hi", meaning: "day/sun" },
      { char: "月", reading: "tsuki", meaning: "month/moon" }, { char: "年", reading: "toshi", meaning: "year" }, { char: "時", reading: "ji", meaning: "time" }
    ];

    // Global variables
    let currentCharIndex = 0;
    let currentFlashcardIndex = 0;
    let currentCharSet = hiraganaChars;
    let soundEnabled = true;
    let musicEnabled = false;
    let isDarkTheme = false;
    let favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    let userProgress = JSON.parse(localStorage.getItem('userProgress') || '{}');
    let studyStats = JSON.parse(localStorage.getItem('studyStats') || '{ "wordsLearned": 0, "studyStreak": 0, "totalStudyTime": 0 }');

    // Audio context for sound effects
    let audioContext;
    let backgroundMusic;

    // Utility Functions
    function playSound(frequency = 440, duration = 200, type = 'sine') {
      if (!soundEnabled) return;

      try {
        if (!audioContext) {
          audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }

        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.value = frequency;
        oscillator.type = type;

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration / 1000);
      } catch (e) {
        console.log('Audio not supported');
      }
    }

    function speakText(text, lang = 'ja-JP') {
      console.log('speakText called with:', text, 'and lang:', lang);
      if (!soundEnabled) {
        console.log('Sound is disabled, returning.');
        return;
      }

      // Check if we're in offline mode
      let isOffline = false;
      if (typeof Android !== 'undefined' && typeof Android.isOfflineMode === 'function') {
        try {
          isOffline = Android.isOfflineMode();
          console.log('Offline mode:', isOffline);
        } catch (error) {
          console.error('Error checking offline mode:', error);
        }
      }

      // Try Android TTS first (for mobile app)
      if (typeof Android !== 'undefined' && typeof Android.speak === 'function') {
        console.log('Calling Android.speak with:', text, '(offline mode:', isOffline, ')');
        try {
          Android.speak(text);
          console.log('Android.speak executed successfully');
          return;
        } catch (error) {
          console.error('Error calling Android.speak:', error);
        }
      } else {
        console.log('Android.speak not available, checking Web Speech API...');
      }

      // Fallback to Web Speech API (only if online or if offline but available)
      if ('speechSynthesis' in window) {
        console.log('Using Web Speech API fallback (offline mode:', isOffline, ')');
        try {
          const utterance = new SpeechSynthesisUtterance(text);
          utterance.lang = lang;
          utterance.rate = 0.8; // Slightly slower for better pronunciation
          utterance.pitch = 1.0;
          utterance.volume = 1.0;

          utterance.onstart = () => console.log('Speech started');
          utterance.onend = () => console.log('Speech ended');
          utterance.onerror = (event) => {
            console.error('Speech error:', event.error);
            if (isOffline) {
              showOfflineAudioMessage();
            }
          };

          speechSynthesis.speak(utterance);
        } catch (error) {
          console.error('Error with Web Speech API:', error);
          if (isOffline) {
            showOfflineAudioMessage();
          }
        }
      } else {
        console.error('No speech synthesis available');
        // Show user feedback that audio is not available
        if (isOffline) {
          showOfflineAudioMessage();
        } else {
          showAudioNotAvailableMessage();
        }
      }
    }

    function showAudioNotAvailableMessage() {
      // Create a temporary notification for the user
      const notification = document.createElement('div');
      notification.textContent = 'Audio not available on this device';
      notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg z-50';
      document.body.appendChild(notification);
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 3000);
    }

    function showOfflineAudioMessage() {
      // Create a temporary notification for offline mode
      const notification = document.createElement('div');
      notification.innerHTML = `
        <div class="flex items-center gap-2">
          <i class="fas fa-wifi-slash"></i>
          <span>Offline Mode - Limited audio available</span>
        </div>
      `;
      notification.className = 'fixed top-4 right-4 bg-orange-500 text-white px-4 py-2 rounded-lg z-50';
      document.body.appendChild(notification);
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 4000);
    }

    function showConnectivityStatus() {
      if (typeof Android !== 'undefined' && typeof Android.getConnectivityStatus === 'function') {
        try {
          const status = Android.getConnectivityStatus();
          console.log('Connectivity status:', status);

          const notification = document.createElement('div');
          notification.innerHTML = `
            <div class="flex items-center gap-2">
              <i class="fas fa-${status === 'online' ? 'wifi' : 'wifi-slash'}"></i>
              <span>${status === 'online' ? 'Online' : 'Offline'} Mode</span>
            </div>
          `;
          notification.className = `fixed top-4 left-4 bg-${status === 'online' ? 'green' : 'orange'}-500 text-white px-4 py-2 rounded-lg z-50`;
          document.body.appendChild(notification);
          setTimeout(() => {
            if (notification.parentNode) {
              notification.parentNode.removeChild(notification);
            }
          }, 2000);
        } catch (error) {
          console.error('Error checking connectivity status:', error);
        }
      }
    }

    function saveProgress() {
      localStorage.setItem('userProgress', JSON.stringify(userProgress));
      localStorage.setItem('studyStats', JSON.stringify(studyStats));
      localStorage.setItem('favorites', JSON.stringify(favorites));
    }

    function updateDashboard() {
      document.getElementById('wordsLearned').textContent = studyStats.wordsLearned || 0;
      document.getElementById('studyStreak').textContent = studyStats.studyStreak || 0;
      document.getElementById('timeStudied').textContent = Math.floor((studyStats.totalStudyTime || 0) / 60) + 'h';

      // Update progress bars with correct calculations
      const hiraganaProgress = Math.min(100, Math.round((userProgress.hiragana || 0) / hiraganaChars.length * 100));
      const katakanaProgress = Math.min(100, Math.round((userProgress.katakana || 0) / katakanaChars.length * 100));
      const vocabProgress = Math.min(100, Math.round((studyStats.wordsLearned || 0) / dictionary.length * 100));

      document.getElementById('hiraganaProgress').textContent = hiraganaProgress + '%';
      document.getElementById('hiraganaBar').style.width = hiraganaProgress + '%';

      document.getElementById('katakanaProgress').textContent = katakanaProgress + '%';
      document.getElementById('katakanaBar').style.width = katakanaProgress + '%';

      document.getElementById('vocabProgress').textContent = vocabProgress + '%';
      document.getElementById('vocabBar').style.width = vocabProgress + '%';
    }

    function addToRecentActivity(activity) {
      const recentActivity = document.getElementById('recentActivity');
      const activityItem = document.createElement('div');
      activityItem.className = 'text-sm text-white text-opacity-80 mb-2';
      activityItem.innerHTML = `<i class="fas fa-check-circle mr-2 text-green-400"></i>${activity}`;

      if (recentActivity.children.length >= 5) {
        recentActivity.removeChild(recentActivity.lastChild);
      }

      recentActivity.insertBefore(activityItem, recentActivity.firstChild);
    }

    // Dictionary Search and Display
    const searchInput = document.getElementById("searchInput");
    const dictionaryResults = document.getElementById("dictionaryResults");
    const categoryFilter = document.getElementById("categoryFilter");
    const difficultyFilter = document.getElementById("difficultyFilter");
    const favoriteFilter = document.getElementById("favoriteFilter");
    let showFavoritesOnly = false;

    function filterDictionary() {
      const query = searchInput.value.toLowerCase();
      const category = categoryFilter.value;
      const difficulty = difficultyFilter.value;

      let filteredWords = dictionary.filter(item => {
        const matchesSearch = !query ||
          item.word.includes(query) ||
          item.reading.toLowerCase().includes(query) ||
          item.english.toLowerCase().includes(query);

        const matchesCategory = !category || item.category === category;
        const matchesDifficulty = !difficulty || item.difficulty === difficulty;
        const matchesFavorites = !showFavoritesOnly || favorites.includes(item.word);

        return matchesSearch && matchesCategory && matchesDifficulty && matchesFavorites;
      });

      displayDictionaryResults(filteredWords);
      document.getElementById('resultsCount').textContent = filteredWords.length;
    }

    function displayDictionaryResults(words) {
      dictionaryResults.innerHTML = "";

      words.forEach(item => {
        const div = document.createElement("div");
        div.className = "glass-effect p-4 rounded-xl card-hover";

        const isFavorite = favorites.includes(item.word);
        const difficultyColor = {
          'Beginner': 'text-green-400',
          'Intermediate': 'text-yellow-400',
          'Advanced': 'text-red-400'
        }[item.difficulty] || 'text-white';

        div.innerHTML = `
          <div class="flex justify-between items-start mb-3">
            <div>
              <h3 class="text-2xl font-bold text-white mb-1">${item.word}</h3>
              <p class="text-lg text-white text-opacity-80">${item.reading}</p>
            </div>
            <div class="flex gap-2">
              <button class="favorite-btn text-${isFavorite ? 'red' : 'white'}-400 hover:text-red-300 transition-colors" data-word="${item.word}">
                <i class="fas fa-heart"></i>
              </button>
              <button class="speak-btn text-white text-opacity-60 hover:text-white transition-colors" data-word="${item.word}">
                <i class="fas fa-volume-up"></i>
              </button>
            </div>
          </div>

          <div class="mb-3">
            <p class="text-xl text-white mb-2">${item.english}</p>
            <div class="flex gap-2 mb-2">
              <span class="px-2 py-1 bg-white bg-opacity-20 rounded text-sm text-white">${item.category}</span>
              <span class="px-2 py-1 bg-white bg-opacity-20 rounded text-sm ${difficultyColor}">${item.difficulty}</span>
            </div>
          </div>

          <div class="border-t border-white border-opacity-20 pt-3">
            <div class="flex justify-between items-center mb-2">
              <p class="text-white text-opacity-80">Example:</p>
              <div class="flex gap-1">
                <button class="speak-example-jp-btn text-white text-opacity-60 hover:text-white transition-colors text-sm" data-text="${item.example}">
                  <i class="fas fa-volume-up"></i> JP
                </button>
                <button class="speak-example-en-btn text-white text-opacity-60 hover:text-white transition-colors text-sm" data-text="${item.exampleTranslation}">
                  <i class="fas fa-volume-up"></i> EN
                </button>
              </div>
            </div>
            <p class="text-white mb-1 cursor-pointer speak-example-jp-text" data-text="${item.example}">${item.example}</p>
            <p class="text-white text-opacity-60 text-sm cursor-pointer speak-example-en-text" data-text="${item.exampleTranslation}">${item.exampleTranslation}</p>
          </div>
        `;

        dictionaryResults.appendChild(div);

        // Add event listeners for this specific dictionary item
        const speakBtn = div.querySelector('.speak-btn');
        const favoriteBtn = div.querySelector('.favorite-btn');
        const speakExampleJpBtns = div.querySelectorAll('.speak-example-jp-btn, .speak-example-jp-text');
        const speakExampleEnBtns = div.querySelectorAll('.speak-example-en-btn, .speak-example-en-text');

        if (speakBtn) {
          speakBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('=== Dictionary speak button clicked ===');
            console.log('Word:', item.word);
            console.log('soundEnabled:', soundEnabled);
            console.log('About to call speakText with:', item.word);
            speakText(item.word);
            console.log('speakText call completed');
          });
          console.log('Event listener added for speak button, word:', item.word);
        } else {
          console.error('Speak button not found for word:', item.word);
        }

        if (favoriteBtn) {
          favoriteBtn.addEventListener('click', () => {
            toggleFavorite(item.word);
          });
        }

        speakExampleJpBtns.forEach(btn => {
          btn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('=== Dictionary example JP clicked ===');
            console.log('Example text:', item.example);
            console.log('soundEnabled:', soundEnabled);
            console.log('About to call speakText with Japanese example');
            speakText(item.example);
            console.log('speakText call completed for Japanese example');
          });
        });

        speakExampleEnBtns.forEach(btn => {
          btn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('=== Dictionary example EN clicked ===');
            console.log('Example text:', item.exampleTranslation);
            console.log('About to call speakText with English example');
            speakText(item.exampleTranslation, 'en-US');
            console.log('speakText call completed for English example');
          });
        });
      });
    }

    function toggleFavorite(word) {
      const index = favorites.indexOf(word);
      if (index > -1) {
        favorites.splice(index, 1);
        playSound(300, 150);
      } else {
        favorites.push(word);
        playSound(600, 150);
      }
      saveProgress();
      filterDictionary();
    }

    // Event Listeners for Dictionary
    searchInput.addEventListener("input", filterDictionary);
    categoryFilter.addEventListener("change", filterDictionary);
    difficultyFilter.addEventListener("change", filterDictionary);
    favoriteFilter.addEventListener("click", () => {
      showFavoritesOnly = !showFavoritesOnly;
      favoriteFilter.classList.toggle('bg-red-500');
      favoriteFilter.classList.toggle('bg-opacity-50');
      filterDictionary();
    });

    // Global click handler for dictionary buttons (fallback)
    document.addEventListener('click', (e) => {
      if (e.target.closest('.speak-btn')) {
        const btn = e.target.closest('.speak-btn');
        const word = btn.getAttribute('data-word');
        if (word) {
          console.log('Global handler: Dictionary speak button clicked for word:', word);
          e.preventDefault();
          e.stopPropagation();
          speakText(word);
        }
      }
    });

    // Writing Practice
    const canvas = document.getElementById("writingCanvas");
    const ctx = canvas.getContext("2d");
    let isDrawing = false;

    function setupWritingCanvas() {
      ctx.strokeStyle = '#667eea';
      ctx.lineWidth = 3;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
    }

    function updateCurrentCharacter() {
      const currentChar = currentCharSet[currentCharIndex];
      document.getElementById("currentChar").textContent = currentChar.char;
      document.getElementById("currentCharReading").textContent = currentChar.reading;
      document.getElementById("currentCharMeaning").textContent = currentChar.meaning || '';
    }

    // Canvas drawing events
    canvas.addEventListener("mousedown", (e) => {
      isDrawing = true;
      const rect = canvas.getBoundingClientRect();
      ctx.beginPath();
      ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
    });

    canvas.addEventListener("mouseup", () => {
      isDrawing = false;
    });

    canvas.addEventListener("mousemove", (e) => {
      if (isDrawing) {
        const rect = canvas.getBoundingClientRect();
        ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
        ctx.stroke();
      }
    });

    // Touch events for mobile
    canvas.addEventListener("touchstart", (e) => {
      e.preventDefault();
      isDrawing = true;
      const rect = canvas.getBoundingClientRect();
      const touch = e.touches[0];
      ctx.beginPath();
      ctx.moveTo(touch.clientX - rect.left, touch.clientY - rect.top);
    });

    canvas.addEventListener("touchend", (e) => {
      e.preventDefault();
      isDrawing = false;
    });

    canvas.addEventListener("touchmove", (e) => {
      e.preventDefault();
      if (isDrawing) {
        const rect = canvas.getBoundingClientRect();
        const touch = e.touches[0];
        ctx.lineTo(touch.clientX - rect.left, touch.clientY - rect.top);
        ctx.stroke();
      }
    });

    // Writing practice controls
    document.getElementById("clearCanvas").addEventListener("click", () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.beginPath();
      playSound(400, 100);
    });

    document.getElementById("nextChar").addEventListener("click", () => {
      currentCharIndex = (currentCharIndex + 1) % currentCharSet.length;
      updateCurrentCharacter();
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.beginPath();
      playSound(500, 150);

      // Update progress
      const charType = currentCharSet === hiraganaChars ? 'hiragana' :
                      currentCharSet === katakanaChars ? 'katakana' : 'kanji';
      userProgress[charType] = Math.max(userProgress[charType] || 0, currentCharIndex + 1);
      saveProgress();
      updateDashboard();
      addToRecentActivity(`Practiced ${currentCharSet[currentCharIndex].char} (${currentCharSet[currentCharIndex].reading})`);
    });

    document.getElementById("playSound").addEventListener("click", () => {
      const currentChar = currentCharSet[currentCharIndex];
      speakText(currentChar.reading);
    });

    // Character set switching
    document.getElementById("hiraganaMode").addEventListener("click", () => {
      currentCharSet = hiraganaChars;
      currentCharIndex = 0;
      updateCurrentCharacter();
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      playSound(600, 150);
    });

    document.getElementById("katakanaMode").addEventListener("click", () => {
      currentCharSet = katakanaChars;
      currentCharIndex = 0;
      updateCurrentCharacter();
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      playSound(600, 150);
    });

    document.getElementById("kanjiMode").addEventListener("click", () => {
      currentCharSet = kanjiChars;
      currentCharIndex = 0;
      updateCurrentCharacter();
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      playSound(600, 150);
    });

    // Flashcards
    let isFlashcardFlipped = false;

    function updateFlashcard() {
      const item = dictionary[currentFlashcardIndex];
      document.getElementById("flashcardWord").textContent = item.word;
      document.getElementById("flashcardReading").textContent = item.reading;
      document.getElementById("flashcardEnglish").textContent = item.english;
      document.getElementById("flashcardExample").textContent = item.example;
      document.getElementById("flashcardExampleTranslation").textContent = item.exampleTranslation;
      document.getElementById("currentCardNumber").textContent = currentFlashcardIndex + 1;
      document.getElementById("totalCards").textContent = dictionary.length;

      // Reset to front side
      isFlashcardFlipped = false;
      document.getElementById("flashcardFront").classList.remove("hidden");
      document.getElementById("flashcardBack").classList.add("hidden");
    }

    function flipFlashcard() {
      isFlashcardFlipped = !isFlashcardFlipped;
      if (isFlashcardFlipped) {
        document.getElementById("flashcardFront").classList.add("hidden");
        document.getElementById("flashcardBack").classList.remove("hidden");
      } else {
        document.getElementById("flashcardFront").classList.remove("hidden");
        document.getElementById("flashcardBack").classList.add("hidden");
      }
      playSound(450, 100);
    }

    // Flashcard event listeners
    document.getElementById("flipCard").addEventListener("click", flipFlashcard);

    document.getElementById("prevFlashcard").addEventListener("click", () => {
      currentFlashcardIndex = (currentFlashcardIndex - 1 + dictionary.length) % dictionary.length;
      updateFlashcard();
      playSound(400, 100);
    });

    document.getElementById("nextFlashcard").addEventListener("click", () => {
      currentFlashcardIndex = (currentFlashcardIndex + 1) % dictionary.length;
      updateFlashcard();
      playSound(500, 100);

      // Update study stats
      studyStats.wordsLearned = Math.max(studyStats.wordsLearned || 0, currentFlashcardIndex + 1);
      saveProgress();
      updateDashboard();
      addToRecentActivity(`Studied flashcard: ${dictionary[currentFlashcardIndex].word}`);
    });

    document.getElementById("speakJapanese").addEventListener("click", () => {
      const item = dictionary[currentFlashcardIndex];
      speakText(item.word);
    });

    document.getElementById("speakEnglish").addEventListener("click", () => {
      const item = dictionary[currentFlashcardIndex];
      speakText(item.english, 'en-US');
    });

    document.getElementById("favoriteCard").addEventListener("click", () => {
      const word = dictionary[currentFlashcardIndex].word;
      toggleFavorite(word);
    });

    // Flashcard example sound functions
    function speakFlashcardExample() {
      const item = dictionary[currentFlashcardIndex];
      speakText(item.example);
    }

    function speakFlashcardExampleTranslation() {
      const item = dictionary[currentFlashcardIndex];
      speakText(item.exampleTranslation, 'en-US');
    }

    document.getElementById("speakExampleJP").addEventListener("click", speakFlashcardExample);
    document.getElementById("speakExampleEN").addEventListener("click", speakFlashcardExampleTranslation);

    // Difficulty rating for spaced repetition
    document.getElementById("difficultyHard").addEventListener("click", () => {
      // Mark as hard - will appear more frequently
      playSound(300, 150);
      addToRecentActivity("Marked word as difficult");
    });

    document.getElementById("difficultyMedium").addEventListener("click", () => {
      // Mark as medium
      playSound(400, 150);
      addToRecentActivity("Marked word as medium difficulty");
    });

    document.getElementById("difficultyEasy").addEventListener("click", () => {
      // Mark as easy - will appear less frequently
      playSound(600, 150);
      addToRecentActivity("Marked word as easy");
    });

    // Sentence Making
    const sentences = [
      { english: "I eat an apple.", japanese: "りんごを食べます。", parts: ["りんご", "を", "食べます"] },
      { english: "I read a book.", japanese: "本を読みます。", parts: ["本", "を", "読みます"] },
      { english: "I drink water.", japanese: "みずを飲みます。", parts: ["みず", "を", "飲みます"] },
      { english: "Good morning.", japanese: "おはようございます。", parts: ["おはよう", "ございます"] },
      { english: "Thank you.", japanese: "ありがとうございます。", parts: ["ありがとう", "ございます"] }
    ];

    let currentSentenceIndex = 0;

    function setupSentenceMaking() {
      const sentence = sentences[currentSentenceIndex];
      document.getElementById("sentencePrompt").textContent = `"${sentence.english}"`;

      const wordBank = document.getElementById("wordBank");
      wordBank.innerHTML = "";

      // Shuffle parts and add distractors
      const shuffledParts = [...sentence.parts, "は", "に", "が", "で"].sort(() => Math.random() - 0.5);

      shuffledParts.forEach(part => {
        const div = document.createElement("div");
        div.textContent = part;
        div.className = "glass-effect text-white px-4 py-2 rounded-lg cursor-move hover:bg-white hover:bg-opacity-20 transition-all";
        div.draggable = true;
        div.addEventListener("dragstart", (e) => e.dataTransfer.setData("text", part));
        wordBank.appendChild(div);
      });

      const sentenceArea = document.getElementById("sentenceArea");
      sentenceArea.innerHTML = '<span class="text-white text-opacity-50">Drop words here...</span>';

      sentenceArea.addEventListener("dragover", (e) => e.preventDefault());
      sentenceArea.addEventListener("drop", (e) => {
        e.preventDefault();
        const word = e.dataTransfer.getData("text");

        if (sentenceArea.querySelector('.text-opacity-50')) {
          sentenceArea.innerHTML = '';
        }

        const wordDiv = document.createElement("div");
        wordDiv.textContent = word;
        wordDiv.className = "glass-effect text-white px-3 py-2 rounded-lg inline-block mr-2 mb-2 cursor-pointer";
        wordDiv.onclick = () => wordDiv.remove();
        sentenceArea.appendChild(wordDiv);
        playSound(450, 100);
      });
    }

    document.getElementById("clearSentence").addEventListener("click", () => {
      const sentenceArea = document.getElementById("sentenceArea");
      sentenceArea.innerHTML = '<span class="text-white text-opacity-50">Drop words here...</span>';
      playSound(400, 100);
    });

    document.getElementById("checkSentence").addEventListener("click", () => {
      const sentenceArea = document.getElementById("sentenceArea");
      const userWords = Array.from(sentenceArea.children)
        .filter(div => !div.classList.contains('text-opacity-50'))
        .map(div => div.textContent);
      const userSentence = userWords.join("");
      const correctSentence = sentences[currentSentenceIndex].japanese;
      const feedback = document.getElementById("sentenceFeedback");

      if (userSentence === correctSentence) {
        feedback.innerHTML = '<i class="fas fa-check-circle text-green-400 mr-2"></i>Correct! Great job!';
        feedback.className = "text-center text-green-400 text-lg";
        playSound(600, 300);
        addToRecentActivity(`Completed sentence: ${sentences[currentSentenceIndex].english}`);
      } else {
        feedback.innerHTML = `<i class="fas fa-times-circle text-red-400 mr-2"></i>Try again! Correct: ${correctSentence}`;
        feedback.className = "text-center text-red-400 text-lg";
        playSound(300, 300);
      }
    });

    document.getElementById("speakSentence").addEventListener("click", () => {
      const sentenceArea = document.getElementById("sentenceArea");
      const userWords = Array.from(sentenceArea.children)
        .filter(div => !div.classList.contains('text-opacity-50'))
        .map(div => div.textContent);
      const userSentence = userWords.join("");

      if (userSentence) {
        speakText(userSentence);
      }
    });

    document.getElementById("nextSentence").addEventListener("click", () => {
      currentSentenceIndex = (currentSentenceIndex + 1) % sentences.length;
      setupSentenceMaking();
      document.getElementById("sentenceFeedback").textContent = "";
      playSound(500, 150);
    });

    // Theme and Settings Controls
    document.getElementById("themeToggle").addEventListener("click", () => {
      isDarkTheme = !isDarkTheme;
      const body = document.getElementById("app-body");
      const icon = document.querySelector("#themeToggle i");

      if (isDarkTheme) {
        body.classList.add("theme-dark");
        icon.className = "fas fa-sun";
      } else {
        body.classList.remove("theme-dark");
        icon.className = "fas fa-moon";
      }

      playSound(500, 100);
      localStorage.setItem('isDarkTheme', isDarkTheme);
    });

    document.getElementById("soundToggle").addEventListener("click", () => {
      soundEnabled = !soundEnabled;
      const icon = document.querySelector("#soundToggle i");

      if (soundEnabled) {
        icon.className = "fas fa-volume-up";
      } else {
        icon.className = "fas fa-volume-mute";
      }

      if (soundEnabled) playSound(500, 100);
      localStorage.setItem('soundEnabled', soundEnabled);
    });

    document.getElementById("musicToggle").addEventListener("click", () => {
      musicEnabled = !musicEnabled;
      const icon = document.querySelector("#musicToggle i");

      if (musicEnabled) {
        icon.className = "fas fa-music";
        // Could add background music here
      } else {
        icon.className = "fas fa-music";
        // Stop background music
      }

      playSound(500, 100);
      localStorage.setItem('musicEnabled', musicEnabled);
    });

    document.getElementById("testAudioButton").addEventListener("click", () => {
      console.log('Test audio button clicked - testing Japanese pronunciation');
      // Test with a Japanese word directly
      console.log('Testing with Japanese word: りんご');
      speakText('りんご');
    });

    // Navigation
    document.querySelectorAll(".nav-link").forEach(link => {
      link.addEventListener("click", (e) => {
        e.preventDefault();
        const targetId = link.getAttribute("href");

        // Hide all sections
        document.querySelectorAll("section").forEach(section => {
          section.classList.add("hidden");
        });

        // Show target section
        const targetSection = document.querySelector(targetId);
        if (targetSection) {
          targetSection.classList.remove("hidden");
          targetSection.classList.add("slide-in");
        }

        // Initialize section-specific functionality
        if (targetId === "#sentence") {
          setupSentenceMaking();
        } else if (targetId === "#writing") {
          setupWritingCanvas();
          updateCurrentCharacter();
        } else if (targetId === "#dictionary") {
          filterDictionary();
        } else if (targetId === "#dashboard") {
          updateDashboard();
        }

        playSound(450, 100);
      });
    });

    // Debug function to test audio functionality
    function testAudio() {
      console.log('=== Audio System Test ===');
      console.log('soundEnabled:', soundEnabled);
      console.log('Android object available:', typeof Android !== 'undefined');
      console.log('Android.speak function available:', typeof Android !== 'undefined' && typeof Android.speak === 'function');
      console.log('Web Speech API available:', 'speechSynthesis' in window);

      // Just check availability, don't play test sound
      if (typeof Android !== 'undefined' && typeof Android.speak === 'function') {
        console.log('Android TTS is available');
      } else if ('speechSynthesis' in window) {
        console.log('Web Speech API is available');
      } else {
        console.log('No audio system available');
      }
    }

    // Test function for dictionary words - call from console: testDictionaryWord('りんご')
    window.testDictionaryWord = function(word) {
      console.log('=== Testing Dictionary Word ===');
      console.log('Word:', word);
      console.log('soundEnabled:', soundEnabled);
      speakText(word);
    }

    // Test function to check if sound is enabled
    window.checkSoundStatus = function() {
      console.log('=== Sound Status ===');
      console.log('soundEnabled:', soundEnabled);
      console.log('Android available:', typeof Android !== 'undefined');
      console.log('Android.speak available:', typeof Android !== 'undefined' && typeof Android.speak === 'function');
      console.log('Web Speech available:', 'speechSynthesis' in window);

      // Test Android interface directly
      if (typeof Android !== 'undefined' && typeof Android.speak === 'function') {
        console.log('Testing Android.speak directly...');
        try {
          Android.speak('test');
          console.log('Android.speak call successful');
        } catch (error) {
          console.error('Android.speak call failed:', error);
        }
      }

      return {
        soundEnabled,
        androidAvailable: typeof Android !== 'undefined',
        androidSpeakAvailable: typeof Android !== 'undefined' && typeof Android.speak === 'function',
        webSpeechAvailable: 'speechSynthesis' in window
      };
    }

    // Force Android TTS test
    window.testAndroidTTS = function(text = 'test') {
      console.log('=== Testing Android TTS Directly ===');
      if (typeof Android !== 'undefined' && typeof Android.speak === 'function') {
        console.log('Calling Android.speak with:', text);
        Android.speak(text);
        console.log('Android.speak call completed');
      } else {
        console.error('Android TTS not available');
      }
    }

    // Check Android TTS engine status
    window.checkAndroidTTSStatus = function() {
      console.log('=== Checking Android TTS Engine Status ===');
      if (typeof Android !== 'undefined' && typeof Android.getTTSStatus === 'function') {
        const status = Android.getTTSStatus();
        console.log('Android TTS Status:', status);
        return status;
      } else {
        console.error('Android.getTTSStatus not available');
        return 'Android TTS interface not available';
      }
    }

    // Test Japanese word specifically
    window.testJapaneseWord = function(word = 'りんご') {
      console.log('=== Testing Japanese Word ===');
      console.log('Word:', word);

      if (typeof Android !== 'undefined' && typeof Android.speak === 'function') {
        console.log('Using Android TTS for Japanese word');
        Android.speak(word);
      } else {
        console.log('Using Web Speech API for Japanese word');
        speakText(word);
      }
    }

    // Install Japanese TTS
    window.installJapaneseTTS = function() {
      console.log('=== Installing Japanese TTS ===');
      if (typeof Android !== 'undefined' && typeof Android.installJapaneseTTS === 'function') {
        Android.installJapaneseTTS();
        console.log('Japanese TTS installation requested');
      } else {
        console.error('Android.installJapaneseTTS not available');
      }
    }

    // Test Japanese sentence specifically
    window.testJapaneseSentence = function(sentence = 'りんごを食べます。') {
      console.log('=== Testing Japanese Sentence ===');
      console.log('Sentence:', sentence);

      if (typeof Android !== 'undefined' && typeof Android.speak === 'function') {
        console.log('Using Android TTS for Japanese sentence');
        Android.speak(sentence);
      } else {
        console.log('Using Web Speech API for Japanese sentence');
        speakText(sentence);
      }
    }

    // Initialize Application
    function initializeApp() {
      // Load saved settings
      const savedTheme = localStorage.getItem('isDarkTheme');
      const savedSound = localStorage.getItem('soundEnabled');
      const savedMusic = localStorage.getItem('musicEnabled');

      if (savedTheme === 'true') {
        document.getElementById("themeToggle").click();
      }

      if (savedSound === 'false') {
        document.getElementById("soundToggle").click();
      }

      if (savedMusic === 'true') {
        document.getElementById("musicToggle").click();
      }

      // Initialize audio system (no test sound)
      console.log('Audio system initialized');

      // Show connectivity status after a short delay
      setTimeout(() => {
        showConnectivityStatus();
      }, 1500);

      // Calculate dynamic counts
      const totalWords = dictionary.length;
      const uniqueCategories = [...new Set(dictionary.map(item => item.category))].sort();
      const totalCategories = uniqueCategories.length;

      // Update counts in UI
      document.getElementById('totalWords').textContent = totalWords;
      document.getElementById('totalCategories').textContent = totalCategories;

      // Populate category filter dynamically
      const categoryFilter = document.getElementById('categoryFilter');
      uniqueCategories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categoryFilter.appendChild(option);
      });

      // Initialize components
      setupWritingCanvas();
      updateCurrentCharacter();
      updateFlashcard();
      setupSentenceMaking();
      filterDictionary();
      updateDashboard();

      // Show dashboard by default
      document.querySelector('#dashboard').classList.remove('hidden');

      // Add welcome message
      addToRecentActivity("Welcome to Learn Japanese! Start your journey today.");

      console.log(`Learn Japanese App initialized with ${totalWords} words across ${totalCategories} categories:`, uniqueCategories);
    }

    // Start the application when page loads
    document.addEventListener('DOMContentLoaded', initializeApp);
  </script>
</body>
</html>