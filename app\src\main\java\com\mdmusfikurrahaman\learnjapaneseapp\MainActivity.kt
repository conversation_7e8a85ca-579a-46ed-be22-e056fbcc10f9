package com.mdmusfikurrahaman.learnjapaneseapp

import android.annotation.SuppressLint
import android.os.Bundle
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebChromeClient
import android.webkit.WebViewClient
import android.speech.tts.TextToSpeech
import java.util.Locale
import android.content.Context
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import com.mdmusfikurrahaman.learnjapaneseapp.ui.theme.LearnJapaneseAppTheme
import com.google.android.gms.ads.MobileAds
import com.google.android.gms.ads.AdView
import com.google.android.gms.ads.AdRequest
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView

class MainActivity : ComponentActivity(), TextToSpeech.OnInitListener {
    private var tts: TextToSpeech? = null

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        MobileAds.initialize(this) {}
        tts = TextToSpeech(this, this)
        setContent {
            LearnJapaneseAppTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    Column(modifier = Modifier.fillMaxSize()) {
                        WebViewScreen(modifier = Modifier.weight(1f), mainActivity = this@MainActivity)
                        BannerAdView(modifier = Modifier.fillMaxWidth().wrapContentHeight())
                    }
                }
            }
        }
    }

    override fun onInit(status: Int) {
        Log.d("TTS", "TTS onInit called with status: $status")
        if (status == TextToSpeech.SUCCESS) {
            // Set default to English first for stability
            tts?.setLanguage(Locale.ENGLISH)
            Log.i("TTS", "TTS initialized successfully with English as default")

            // Check available languages
            checkAvailableLanguages()

        } else {
            Log.e("TTS", "TTS initialization failed with status: $status")
        }
    }

    private fun checkAvailableLanguages() {
        Log.d("TTS", "=== Checking Available TTS Languages ===")

        val testLocales = listOf(
            Locale.JAPANESE to "Japanese",
            Locale.JAPAN to "Japan",
            Locale("ja", "JP") to "ja-JP",
            Locale("ja") to "ja",
            Locale.ENGLISH to "English",
            Locale.getDefault() to "Default"
        )

        testLocales.forEach { (locale, name) ->
            val result = tts?.isLanguageAvailable(locale)
            Log.d("TTS", "$name (${locale.displayName}): $result")
        }
    }

    override fun onDestroy() {
        if (tts != null) {
            tts!!.stop()
            tts!!.shutdown()
        }
        super.onDestroy()
    }

    inner class JavaScriptInterface(private val context: Context) {
        @android.webkit.JavascriptInterface
        fun speak(text: String) {
            Log.d("TTS", "JavaScript interface speak called with: $text")

            // Ensure TTS runs on the main UI thread
            runOnUiThread {
                if (tts != null) {
                    // Check if text contains Japanese characters
                    val isJapanese = text.any { char ->
                        char.code in 0x3040..0x309F || // Hiragana
                        char.code in 0x30A0..0x30FF || // Katakana
                        char.code in 0x4E00..0x9FAF    // Kanji
                    }

                    Log.d("TTS", "Text '$text' is Japanese: $isJapanese")

                    if (isJapanese) {
                        speakJapaneseText(text)
                    } else {
                        // For English text, use normal TTS
                        tts?.setLanguage(Locale.ENGLISH)
                        val result = tts?.speak(text, TextToSpeech.QUEUE_FLUSH, null, "english_word")
                        Log.d("TTS", "English TTS result: $result")
                    }
                } else {
                    Log.e("TTS", "TTS is null, cannot speak")
                }
            }
        }

        private fun speakJapaneseText(text: String) {
            Log.d("TTS", "Attempting to speak Japanese text: $text")

            // First try Japanese TTS
            val langResult = tts?.setLanguage(Locale.JAPANESE)
            Log.d("TTS", "Japanese language set result: $langResult")

            when (langResult) {
                TextToSpeech.LANG_AVAILABLE,
                TextToSpeech.LANG_COUNTRY_AVAILABLE,
                TextToSpeech.LANG_COUNTRY_VAR_AVAILABLE -> {
                    // Japanese is available, use it
                    val speakResult = tts?.speak(text, TextToSpeech.QUEUE_FLUSH, null, "japanese_word")
                    Log.d("TTS", "Japanese TTS speak result: $speakResult")

                    if (speakResult == TextToSpeech.ERROR) {
                        Log.w("TTS", "Japanese TTS speak failed, using romanized fallback")
                        speakRomanizedJapanese(text)
                    }
                }
                else -> {
                    // Japanese not available, use romanized pronunciation
                    Log.w("TTS", "Japanese TTS not available (result: $langResult), using romanized fallback")
                    speakRomanizedJapanese(text)
                }
            }
        }

        private fun speakRomanizedJapanese(text: String) {
            Log.d("TTS", "Using romanized pronunciation for: $text")

            // Check if it's a sentence (contains punctuation or multiple words)
            val isSentence = text.contains("。") || text.contains("、") || text.contains("を") ||
                           text.contains("は") || text.contains("が") || text.contains("に") ||
                           text.contains("で") || text.contains("と") || text.length > 5

            if (isSentence) {
                Log.d("TTS", "Detected Japanese sentence, using simplified approach")
                // For sentences, try a simplified approach
                handleJapaneseSentence(text)
                return
            }

            // Comprehensive Japanese words with their romanized pronunciation
            val romanizedMap = mapOf(
                // Food
                "りんご" to "ringo", "みず" to "mizu", "パン" to "pan", "さかな" to "sakana",
                "にく" to "niku", "やさい" to "yasai", "ごはん" to "gohan", "おちゃ" to "ocha",
                "コーヒー" to "koohii", "たまご" to "tamago", "ぎゅうにゅう" to "gyuunyuu",
                "くだもの" to "kudamono", "ケーキ" to "keeki", "アイスクリーム" to "aisukuriimu",
                "チョコレート" to "chokoreeto", "スープ" to "suupu", "サラダ" to "sarada",
                "ラーメン" to "raamen", "すし" to "sushi", "おかし" to "okashi",

                // Greetings
                "おはよう" to "ohayou", "こんにちは" to "konnichiwa", "こんばんは" to "konbanwa",
                "ありがとう" to "arigatou", "すみません" to "sumimasen", "さようなら" to "sayounara",
                "はじめまして" to "hajimemashite", "げんき" to "genki", "いらっしゃいませ" to "irasshaimase",
                "おつかれさま" to "otsukaresama", "ただいま" to "tadaima", "おかえり" to "okaeri",
                "いただきます" to "itadakimasu", "ごちそうさま" to "gochisousama", "おめでとう" to "omedetou",
                "がんばって" to "ganbatte", "よろしく" to "yoroshiku", "おやすみ" to "oyasumi",

                // Objects
                "本" to "hon", "ペン" to "pen", "かみ" to "kami", "つくえ" to "tsukue",
                "いす" to "isu", "でんわ" to "denwa", "テレビ" to "terebi", "コンピューター" to "konpyuutaa",
                "かばん" to "kaban", "とけい" to "tokei", "くるま" to "kuruma", "じてんしゃ" to "jitensha",

                // Family
                "かぞく" to "kazoku", "おとうさん" to "otousan", "おかあさん" to "okaasan",
                "あに" to "ani", "あね" to "ane", "おとうと" to "otouto", "いもうと" to "imouto",

                // Colors
                "あか" to "aka", "あお" to "ao", "きいろ" to "kiiro", "みどり" to "midori",
                "しろ" to "shiro", "くろ" to "kuro", "ピンク" to "pinku", "オレンジ" to "orenji",

                // Numbers
                "いち" to "ichi", "に" to "ni", "さん" to "san", "よん" to "yon",
                "ご" to "go", "ろく" to "roku", "なな" to "nana", "はち" to "hachi",
                "きゅう" to "kyuu", "じゅう" to "juu",

                // Animals
                "いぬ" to "inu", "ねこ" to "neko", "とり" to "tori", "うま" to "uma",
                "ぞう" to "zou", "さる" to "saru", "うさぎ" to "usagi", "くま" to "kuma",

                // Common words and verbs
                "わたし" to "watashi", "あなた" to "anata", "いえ" to "ie", "がっこう" to "gakkou",
                "しごと" to "shigoto", "ともだち" to "tomodachi", "せんせい" to "sensei",
                "たべます" to "tabemasu", "のみます" to "nomimasu", "いきます" to "ikimasu",
                "きます" to "kimasu", "みます" to "mimasu", "ききます" to "kikimasu"
            )

            val romanized = romanizedMap[text] ?: text
            Log.d("TTS", "Romanized '$text' as '$romanized'")

            // Set to English and speak the romanized version
            tts?.setLanguage(Locale.ENGLISH)
            val result = tts?.speak(romanized, TextToSpeech.QUEUE_FLUSH, null, "romanized_japanese")
            Log.d("TTS", "Romanized TTS result: $result")
        }

        private fun handleJapaneseSentence(sentence: String) {
            Log.d("TTS", "Handling Japanese sentence: $sentence")

            // First try Japanese TTS
            val langResult = tts?.setLanguage(Locale.JAPANESE)
            Log.d("TTS", "Japanese language set for sentence, result: $langResult")

            if (langResult == TextToSpeech.LANG_AVAILABLE ||
                langResult == TextToSpeech.LANG_COUNTRY_AVAILABLE ||
                langResult == TextToSpeech.LANG_COUNTRY_VAR_AVAILABLE) {

                // Japanese TTS is available, try to speak the sentence
                val speakResult = tts?.speak(sentence, TextToSpeech.QUEUE_FLUSH, null, "japanese_sentence")
                Log.d("TTS", "Japanese sentence TTS result: $speakResult")

                if (speakResult != TextToSpeech.ERROR) {
                    Log.d("TTS", "Japanese sentence spoken successfully")
                    return
                }
            }

            // If Japanese TTS failed or not available, break down the sentence
            Log.w("TTS", "Japanese TTS not available for sentences, breaking down: $sentence")
            breakDownAndSpeakSentence(sentence)
        }

        private fun breakDownAndSpeakSentence(sentence: String) {
            Log.d("TTS", "Breaking down Japanese sentence: $sentence")

            // Common sentence patterns with romanized versions
            val sentenceMap = mapOf(
                "りんごを食べます。" to "ringo wo tabemasu",
                "みずを飲みます。" to "mizu wo nomimasu",
                "パンを食べます。" to "pan wo tabemasu",
                "さかなを食べます。" to "sakana wo tabemasu",
                "にくを食べます。" to "niku wo tabemasu",
                "やさいを食べます。" to "yasai wo tabemasu",
                "ごはんを食べます。" to "gohan wo tabemasu",
                "おちゃを飲みます。" to "ocha wo nomimasu",
                "コーヒーを飲みます。" to "koohii wo nomimasu",
                "たまごを食べます。" to "tamago wo tabemasu",
                "ぎゅうにゅうを飲みます。" to "gyuunyuu wo nomimasu",
                "くだものを食べます。" to "kudamono wo tabemasu",
                "ケーキを食べます。" to "keeki wo tabemasu",
                "アイスクリームを食べます。" to "aisukuriimu wo tabemasu",
                "チョコレートを食べます。" to "chokoreeto wo tabemasu",
                "スープを飲みます。" to "suupu wo nomimasu",
                "サラダを食べます。" to "sarada wo tabemasu",
                "ラーメンを食べます。" to "raamen wo tabemasu",
                "すしを食べます。" to "sushi wo tabemasu",
                "おかしを食べます。" to "okashi wo tabemasu",

                // Greetings with context
                "おはようございます。" to "ohayou gozaimasu",
                "こんにちは、元気ですか。" to "konnichiwa, genki desu ka",
                "こんばんは、お疲れ様です。" to "konbanwa, otsukaresama desu",
                "ありがとうございます。" to "arigatou gozaimasu",
                "すみません、質問があります。" to "sumimasen, shitsumon ga arimasu",
                "さようなら、また明日。" to "sayounara, mata ashita",
                "はじめまして、よろしくお願いします。" to "hajimemashite, yoroshiku onegaishimasu",

                // Common actions
                "本を読みます。" to "hon wo yomimasu",
                "テレビを見ます。" to "terebi wo mimasu",
                "音楽を聞きます。" to "ongaku wo kikimasu",
                "学校に行きます。" to "gakkou ni ikimasu",
                "家に帰ります。" to "ie ni kaerimasu",
                "友達と遊びます。" to "tomodachi to asobimasu",
                "日本語を勉強します。" to "nihongo wo benkyou shimasu",
                "写真を撮ります。" to "shashin wo torimasu",
                "手紙を書きます。" to "tegami wo kakimasu",
                "電話をかけます。" to "denwa wo kakemasu"
            )

            // Check if we have a direct translation
            val romanized = sentenceMap[sentence]
            if (romanized != null) {
                Log.d("TTS", "Found romanized version: $romanized")
                tts?.setLanguage(Locale.ENGLISH)
                val result = tts?.speak(romanized, TextToSpeech.QUEUE_FLUSH, null, "romanized_sentence")
                Log.d("TTS", "Romanized sentence TTS result: $result")
                return
            }

            // If no direct match, try to break down by common patterns
            Log.d("TTS", "No direct match found, trying pattern breakdown")
            val breakdown = breakDownByPatterns(sentence)
            if (breakdown.isNotEmpty()) {
                tts?.setLanguage(Locale.ENGLISH)
                val result = tts?.speak(breakdown, TextToSpeech.QUEUE_FLUSH, null, "pattern_breakdown")
                Log.d("TTS", "Pattern breakdown TTS result: $result")
            } else {
                // Final fallback
                Log.w("TTS", "Could not break down sentence, using generic fallback")
                tts?.setLanguage(Locale.ENGLISH)
                tts?.speak("Japanese sentence example", TextToSpeech.QUEUE_FLUSH, null, "generic_fallback")
            }
        }

        private fun breakDownByPatterns(sentence: String): String {
            Log.d("TTS", "Breaking down by patterns: $sentence")

            // Simple pattern matching for common structures
            when {
                sentence.contains("を食べます") -> {
                    val food = sentence.replace("を食べます。", "").replace("を食べます", "")
                    return "$food wo tabemasu"
                }
                sentence.contains("を飲みます") -> {
                    val drink = sentence.replace("を飲みます。", "").replace("を飲みます", "")
                    return "$drink wo nomimasu"
                }
                sentence.contains("に行きます") -> {
                    val place = sentence.replace("に行きます。", "").replace("に行きます", "")
                    return "$place ni ikimasu"
                }
                sentence.contains("を見ます") -> {
                    val item = sentence.replace("を見ます。", "").replace("を見ます", "")
                    return "$item wo mimasu"
                }
                sentence.contains("を読みます") -> {
                    val item = sentence.replace("を読みます。", "").replace("を読みます", "")
                    return "$item wo yomimasu"
                }
                sentence.contains("を聞きます") -> {
                    val item = sentence.replace("を聞きます。", "").replace("を聞きます", "")
                    return "$item wo kikimasu"
                }
                else -> return ""
            }
        }



        @android.webkit.JavascriptInterface
        fun getTTSStatus(): String {
            val status = mutableMapOf<String, Any>()

            status["ttsInitialized"] = tts != null

            if (tts != null) {
                val japaneseAvailable = tts?.isLanguageAvailable(Locale.JAPANESE)
                val englishAvailable = tts?.isLanguageAvailable(Locale.ENGLISH)

                status["japaneseAvailable"] = japaneseAvailable.toString()
                status["englishAvailable"] = englishAvailable.toString()
                status["defaultLanguage"] = tts?.defaultLanguage?.displayName ?: "Unknown"
            }

            val result = status.map { "${it.key}: ${it.value}" }.joinToString(", ")
            Log.d("TTS", "TTS Status: $result")
            return result
        }

        @android.webkit.JavascriptInterface
        fun installJapaneseTTS() {
            Log.d("TTS", "Requesting Japanese TTS installation")
            runOnUiThread {
                val installIntent = android.content.Intent()
                installIntent.action = TextToSpeech.Engine.ACTION_INSTALL_TTS_DATA
                try {
                    startActivity(installIntent)
                    Log.d("TTS", "TTS installation intent started")
                } catch (e: Exception) {
                    Log.e("TTS", "Failed to start TTS installation: ${e.message}")
                }
            }
        }
    }
}

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun WebViewScreen(modifier: Modifier = Modifier, mainActivity: MainActivity) {
    val context = LocalContext.current
    AndroidView(factory = {
                WebView(it).apply {
                    layoutParams = android.view.ViewGroup.LayoutParams(
                        android.view.ViewGroup.LayoutParams.MATCH_PARENT,
                        android.view.ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    webViewClient = WebViewClient()
                    webChromeClient = WebChromeClient()
                    settings.javaScriptEnabled = true
                    settings.mediaPlaybackRequiresUserGesture = false
                    settings.domStorageEnabled = true
                    settings.loadWithOverviewMode = true
                    settings.useWideViewPort = true
                    settings.builtInZoomControls = true
                    settings.displayZoomControls = false
                    settings.setSupportZoom(true)
                    settings.defaultTextEncodingName = "utf-8"
                    settings.pluginState = WebSettings.PluginState.ON
                    settings.allowFileAccess = true
                    settings.allowContentAccess = true
                    addJavascriptInterface(mainActivity.JavaScriptInterface(context), "Android")
                    loadUrl("file:///android_asset/index.html")

                    // Enable WebView debugging
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT) {
                        android.webkit.WebView.setWebContentsDebuggingEnabled(true)
                    }
                }
            }, update = {
                it.loadUrl("file:///android_asset/index.html")
            })
}

@Composable
fun BannerAdView(modifier: Modifier = Modifier) {
    val context = LocalContext.current
    AndroidView(
        modifier = modifier,
        factory = {
            AdView(context).apply {
                setAdSize(com.google.android.gms.ads.AdSize.BANNER)
                adUnitId = context.getString(R.string.banner_ad_unit_id)
                loadAd(AdRequest.Builder().build())
            }
        }
    )
}