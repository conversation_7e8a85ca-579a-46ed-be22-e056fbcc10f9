  SuppressLint android.annotation  Activity android.app  Any android.app.Activity  AudioManager android.app.Activity  BannerAdView android.app.Activity  Bundle android.app.Activity  Column android.app.Activity  ConnectivityManager android.app.Activity  Context android.app.Activity  	Exception android.app.Activity  LearnJapaneseAppTheme android.app.Activity  Locale android.app.Activity  Log android.app.Activity  
MaterialTheme android.app.Activity  	MobileAds android.app.Activity  Modifier android.app.Activity  NetworkCapabilities android.app.Activity  String android.app.Activity  Surface android.app.Activity  TextToSpeech android.app.Activity  
ToneGenerator android.app.Activity  
WebViewScreen android.app.Activity  android android.app.Activity  any android.app.Activity  code android.app.Activity  contains android.app.Activity  fillMaxSize android.app.Activity  fillMaxWidth android.app.Activity  filter android.app.Activity  find android.app.Activity  getSystemService android.app.Activity  isNetworkAvailable android.app.Activity  
isNotEmpty android.app.Activity  
isOfflineMode android.app.Activity  joinToString android.app.Activity  let android.app.Activity  listOf android.app.Activity  map android.app.Activity  mapOf android.app.Activity  mutableMapOf android.app.Activity  onCreate android.app.Activity  	onDestroy android.app.Activity  playOfflineAudioFeedback android.app.Activity  replace android.app.Activity  
runOnUiThread android.app.Activity  set android.app.Activity  
setContent android.app.Activity  
startActivity android.app.Activity  to android.app.Activity  toString android.app.Activity  tts android.app.Activity  weight android.app.Activity  wrapContentHeight android.app.Activity  Context android.content  Intent android.content  Any android.content.Context  AudioManager android.content.Context  BannerAdView android.content.Context  Bundle android.content.Context  CONNECTIVITY_SERVICE android.content.Context  Column android.content.Context  ConnectivityManager android.content.Context  Context android.content.Context  	Exception android.content.Context  LearnJapaneseAppTheme android.content.Context  Locale android.content.Context  Log android.content.Context  
MaterialTheme android.content.Context  	MobileAds android.content.Context  Modifier android.content.Context  NetworkCapabilities android.content.Context  String android.content.Context  Surface android.content.Context  TextToSpeech android.content.Context  
ToneGenerator android.content.Context  
WebViewScreen android.content.Context  android android.content.Context  any android.content.Context  code android.content.Context  contains android.content.Context  fillMaxSize android.content.Context  fillMaxWidth android.content.Context  filter android.content.Context  find android.content.Context  	getString android.content.Context  getSystemService android.content.Context  isNetworkAvailable android.content.Context  
isNotEmpty android.content.Context  
isOfflineMode android.content.Context  joinToString android.content.Context  let android.content.Context  listOf android.content.Context  map android.content.Context  mapOf android.content.Context  mutableMapOf android.content.Context  playOfflineAudioFeedback android.content.Context  replace android.content.Context  
runOnUiThread android.content.Context  set android.content.Context  
setContent android.content.Context  
startActivity android.content.Context  to android.content.Context  toString android.content.Context  tts android.content.Context  weight android.content.Context  wrapContentHeight android.content.Context  Any android.content.ContextWrapper  AudioManager android.content.ContextWrapper  BannerAdView android.content.ContextWrapper  Bundle android.content.ContextWrapper  Column android.content.ContextWrapper  ConnectivityManager android.content.ContextWrapper  Context android.content.ContextWrapper  	Exception android.content.ContextWrapper  LearnJapaneseAppTheme android.content.ContextWrapper  Locale android.content.ContextWrapper  Log android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  	MobileAds android.content.ContextWrapper  Modifier android.content.ContextWrapper  NetworkCapabilities android.content.ContextWrapper  String android.content.ContextWrapper  Surface android.content.ContextWrapper  TextToSpeech android.content.ContextWrapper  
ToneGenerator android.content.ContextWrapper  
WebViewScreen android.content.ContextWrapper  android android.content.ContextWrapper  any android.content.ContextWrapper  code android.content.ContextWrapper  contains android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  fillMaxWidth android.content.ContextWrapper  filter android.content.ContextWrapper  find android.content.ContextWrapper  isNetworkAvailable android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
isOfflineMode android.content.ContextWrapper  joinToString android.content.ContextWrapper  let android.content.ContextWrapper  listOf android.content.ContextWrapper  map android.content.ContextWrapper  mapOf android.content.ContextWrapper  mutableMapOf android.content.ContextWrapper  playOfflineAudioFeedback android.content.ContextWrapper  replace android.content.ContextWrapper  
runOnUiThread android.content.ContextWrapper  set android.content.ContextWrapper  
setContent android.content.ContextWrapper  
startActivity android.content.ContextWrapper  to android.content.ContextWrapper  toString android.content.ContextWrapper  tts android.content.ContextWrapper  weight android.content.ContextWrapper  wrapContentHeight android.content.ContextWrapper  action android.content.Intent  AudioManager 
android.media  MediaPlayer 
android.media  
ToneGenerator 
android.media  STREAM_MUSIC android.media.AudioManager  TONE_PROP_BEEP android.media.ToneGenerator  release android.media.ToneGenerator  	startTone android.media.ToneGenerator  ConnectivityManager android.net  Network android.net  NetworkCapabilities android.net  
activeNetwork android.net.ConnectivityManager  getNetworkCapabilities android.net.ConnectivityManager  NET_CAPABILITY_INTERNET android.net.NetworkCapabilities  
hasCapability android.net.NetworkCapabilities  Build 
android.os  Bundle 
android.os  	putString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  KITKAT android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  	putString android.os.Bundle  TextToSpeech android.speech.tts  Voice android.speech.tts  ERROR android.speech.tts.TextToSpeech  LANG_AVAILABLE android.speech.tts.TextToSpeech  LANG_COUNTRY_AVAILABLE android.speech.tts.TextToSpeech  LANG_COUNTRY_VAR_AVAILABLE android.speech.tts.TextToSpeech  OnInitListener android.speech.tts.TextToSpeech  QUEUE_FLUSH android.speech.tts.TextToSpeech  SUCCESS android.speech.tts.TextToSpeech  defaultLanguage android.speech.tts.TextToSpeech  isLanguageAvailable android.speech.tts.TextToSpeech  let android.speech.tts.TextToSpeech  setLanguage android.speech.tts.TextToSpeech  setPitch android.speech.tts.TextToSpeech  
setSpeechRate android.speech.tts.TextToSpeech  shutdown android.speech.tts.TextToSpeech  speak android.speech.tts.TextToSpeech  stop android.speech.tts.TextToSpeech  voice android.speech.tts.TextToSpeech  voices android.speech.tts.TextToSpeech  ACTION_INSTALL_TTS_DATA &android.speech.tts.TextToSpeech.Engine  KEY_FEATURE_EMBEDDED_SYNTHESIS &android.speech.tts.TextToSpeech.Engine  KEY_FEATURE_NETWORK_SYNTHESIS &android.speech.tts.TextToSpeech.Engine  isNetworkConnectionRequired android.speech.tts.Voice  locale android.speech.tts.Voice  name android.speech.tts.Voice  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  Any  android.view.ContextThemeWrapper  AudioManager  android.view.ContextThemeWrapper  BannerAdView  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  Column  android.view.ContextThemeWrapper  ConnectivityManager  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  LearnJapaneseAppTheme  android.view.ContextThemeWrapper  Locale  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  	MobileAds  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  NetworkCapabilities  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  TextToSpeech  android.view.ContextThemeWrapper  
ToneGenerator  android.view.ContextThemeWrapper  
WebViewScreen  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  any  android.view.ContextThemeWrapper  code  android.view.ContextThemeWrapper  contains  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  fillMaxWidth  android.view.ContextThemeWrapper  filter  android.view.ContextThemeWrapper  find  android.view.ContextThemeWrapper  isNetworkAvailable  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  
isOfflineMode  android.view.ContextThemeWrapper  joinToString  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  map  android.view.ContextThemeWrapper  mapOf  android.view.ContextThemeWrapper  mutableMapOf  android.view.ContextThemeWrapper  playOfflineAudioFeedback  android.view.ContextThemeWrapper  replace  android.view.ContextThemeWrapper  
runOnUiThread  android.view.ContextThemeWrapper  set  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  to  android.view.ContextThemeWrapper  toString  android.view.ContextThemeWrapper  tts  android.view.ContextThemeWrapper  weight  android.view.ContextThemeWrapper  wrapContentHeight  android.view.ContextThemeWrapper  layoutParams android.view.View  LayoutParams android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  JavascriptInterface android.webkit  WebChromeClient android.webkit  WebSettings android.webkit  WebView android.webkit  
WebViewClient android.webkit  PluginState android.webkit.WebSettings  allowContentAccess android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  builtInZoomControls android.webkit.WebSettings  defaultTextEncodingName android.webkit.WebSettings  displayZoomControls android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  loadWithOverviewMode android.webkit.WebSettings   mediaPlaybackRequiresUserGesture android.webkit.WebSettings  pluginState android.webkit.WebSettings  setSupportZoom android.webkit.WebSettings  useWideViewPort android.webkit.WebSettings  ON &android.webkit.WebSettings.PluginState  WebChromeClient android.webkit.WebView  WebSettings android.webkit.WebView  
WebViewClient android.webkit.WebView  addJavascriptInterface android.webkit.WebView  android android.webkit.WebView  apply android.webkit.WebView  layoutParams android.webkit.WebView  loadUrl android.webkit.WebView  setWebContentsDebuggingEnabled android.webkit.WebView  settings android.webkit.WebView  webChromeClient android.webkit.WebView  
webViewClient android.webkit.WebView  ComponentActivity androidx.activity  Any #androidx.activity.ComponentActivity  AudioManager #androidx.activity.ComponentActivity  BannerAdView #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Column #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  ConnectivityManager #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  LearnJapaneseAppTheme #androidx.activity.ComponentActivity  Locale #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  	MobileAds #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  NetworkCapabilities #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  SuppressLint #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  TextToSpeech #androidx.activity.ComponentActivity  
ToneGenerator #androidx.activity.ComponentActivity  
WebViewScreen #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  any #androidx.activity.ComponentActivity  code #androidx.activity.ComponentActivity  contains #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  fillMaxWidth #androidx.activity.ComponentActivity  filter #androidx.activity.ComponentActivity  find #androidx.activity.ComponentActivity  isNetworkAvailable #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  
isOfflineMode #androidx.activity.ComponentActivity  joinToString #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  map #androidx.activity.ComponentActivity  mapOf #androidx.activity.ComponentActivity  mutableMapOf #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  playOfflineAudioFeedback #androidx.activity.ComponentActivity  replace #androidx.activity.ComponentActivity  
runOnUiThread #androidx.activity.ComponentActivity  set #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  to #androidx.activity.ComponentActivity  toString #androidx.activity.ComponentActivity  tts #androidx.activity.ComponentActivity  weight #androidx.activity.ComponentActivity  wrapContentHeight #androidx.activity.ComponentActivity  AudioManager -androidx.activity.ComponentActivity.Companion  BannerAdView -androidx.activity.ComponentActivity.Companion  Bundle -androidx.activity.ComponentActivity.Companion  Column -androidx.activity.ComponentActivity.Companion  Context -androidx.activity.ComponentActivity.Companion  LearnJapaneseAppTheme -androidx.activity.ComponentActivity.Companion  Locale -androidx.activity.ComponentActivity.Companion  Log -androidx.activity.ComponentActivity.Companion  
MaterialTheme -androidx.activity.ComponentActivity.Companion  	MobileAds -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  NetworkCapabilities -androidx.activity.ComponentActivity.Companion  Surface -androidx.activity.ComponentActivity.Companion  TextToSpeech -androidx.activity.ComponentActivity.Companion  
ToneGenerator -androidx.activity.ComponentActivity.Companion  
WebViewScreen -androidx.activity.ComponentActivity.Companion  android -androidx.activity.ComponentActivity.Companion  any -androidx.activity.ComponentActivity.Companion  code -androidx.activity.ComponentActivity.Companion  contains -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  fillMaxWidth -androidx.activity.ComponentActivity.Companion  filter -androidx.activity.ComponentActivity.Companion  find -androidx.activity.ComponentActivity.Companion  isNetworkAvailable -androidx.activity.ComponentActivity.Companion  
isNotEmpty -androidx.activity.ComponentActivity.Companion  
isOfflineMode -androidx.activity.ComponentActivity.Companion  joinToString -androidx.activity.ComponentActivity.Companion  let -androidx.activity.ComponentActivity.Companion  listOf -androidx.activity.ComponentActivity.Companion  map -androidx.activity.ComponentActivity.Companion  mapOf -androidx.activity.ComponentActivity.Companion  mutableMapOf -androidx.activity.ComponentActivity.Companion  playOfflineAudioFeedback -androidx.activity.ComponentActivity.Companion  replace -androidx.activity.ComponentActivity.Companion  
runOnUiThread -androidx.activity.ComponentActivity.Companion  set -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
startActivity -androidx.activity.ComponentActivity.Companion  to -androidx.activity.ComponentActivity.Companion  toString -androidx.activity.ComponentActivity.Companion  tts -androidx.activity.ComponentActivity.Companion  weight -androidx.activity.ComponentActivity.Companion  wrapContentHeight -androidx.activity.ComponentActivity.Companion  webkit +androidx.activity.ComponentActivity.android  JavascriptInterface 2androidx.activity.ComponentActivity.android.webkit  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  wrapContentHeight "androidx.compose.foundation.layout  BannerAdView .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  
WebViewScreen .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  
isOfflineMode .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  wrapContentHeight .androidx.compose.foundation.layout.ColumnScope  ColorScheme androidx.compose.material3  
MaterialTheme androidx.compose.material3  Surface androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
background &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
Composable androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  Modifier androidx.compose.ui  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  wrapContentHeight androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  TextUnit androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Any #androidx.core.app.ComponentActivity  AudioManager #androidx.core.app.ComponentActivity  BannerAdView #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Column #androidx.core.app.ComponentActivity  ConnectivityManager #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  LearnJapaneseAppTheme #androidx.core.app.ComponentActivity  Locale #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  	MobileAds #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  NetworkCapabilities #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  SuppressLint #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  TextToSpeech #androidx.core.app.ComponentActivity  
ToneGenerator #androidx.core.app.ComponentActivity  
WebViewScreen #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  any #androidx.core.app.ComponentActivity  code #androidx.core.app.ComponentActivity  contains #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  fillMaxWidth #androidx.core.app.ComponentActivity  filter #androidx.core.app.ComponentActivity  find #androidx.core.app.ComponentActivity  isNetworkAvailable #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  
isOfflineMode #androidx.core.app.ComponentActivity  joinToString #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  map #androidx.core.app.ComponentActivity  mapOf #androidx.core.app.ComponentActivity  mutableMapOf #androidx.core.app.ComponentActivity  playOfflineAudioFeedback #androidx.core.app.ComponentActivity  replace #androidx.core.app.ComponentActivity  
runOnUiThread #androidx.core.app.ComponentActivity  set #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  to #androidx.core.app.ComponentActivity  toString #androidx.core.app.ComponentActivity  tts #androidx.core.app.ComponentActivity  weight #androidx.core.app.ComponentActivity  wrapContentHeight #androidx.core.app.ComponentActivity  webkit +androidx.core.app.ComponentActivity.android  JavascriptInterface 2androidx.core.app.ComponentActivity.android.webkit  	AdRequest com.google.android.gms.ads  AdSize com.google.android.gms.ads  AdView com.google.android.gms.ads  	MobileAds com.google.android.gms.ads  Builder $com.google.android.gms.ads.AdRequest  build ,com.google.android.gms.ads.AdRequest.Builder  BANNER !com.google.android.gms.ads.AdSize  	AdRequest !com.google.android.gms.ads.AdView  R !com.google.android.gms.ads.AdView  adUnitId !com.google.android.gms.ads.AdView  apply !com.google.android.gms.ads.AdView  com !com.google.android.gms.ads.AdView  loadAd !com.google.android.gms.ads.AdView  	setAdSize !com.google.android.gms.ads.AdView  adUnitId %com.google.android.gms.ads.BaseAdView  loadAd %com.google.android.gms.ads.BaseAdView  	setAdSize %com.google.android.gms.ads.BaseAdView  
initialize $com.google.android.gms.ads.MobileAds  InitializationStatus )com.google.android.gms.ads.initialization   OnInitializationCompleteListener )com.google.android.gms.ads.initialization  <SAM-CONSTRUCTOR> Jcom.google.android.gms.ads.initialization.OnInitializationCompleteListener  	AdRequest &com.mdmusfikurrahaman.learnjapaneseapp  Any &com.mdmusfikurrahaman.learnjapaneseapp  AudioManager &com.mdmusfikurrahaman.learnjapaneseapp  BannerAdView &com.mdmusfikurrahaman.learnjapaneseapp  Boolean &com.mdmusfikurrahaman.learnjapaneseapp  Bundle &com.mdmusfikurrahaman.learnjapaneseapp  Column &com.mdmusfikurrahaman.learnjapaneseapp  ComponentActivity &com.mdmusfikurrahaman.learnjapaneseapp  
Composable &com.mdmusfikurrahaman.learnjapaneseapp  ConnectivityManager &com.mdmusfikurrahaman.learnjapaneseapp  Context &com.mdmusfikurrahaman.learnjapaneseapp  	Exception &com.mdmusfikurrahaman.learnjapaneseapp  Int &com.mdmusfikurrahaman.learnjapaneseapp  LearnJapaneseAppTheme &com.mdmusfikurrahaman.learnjapaneseapp  Locale &com.mdmusfikurrahaman.learnjapaneseapp  Log &com.mdmusfikurrahaman.learnjapaneseapp  MainActivity &com.mdmusfikurrahaman.learnjapaneseapp  
MaterialTheme &com.mdmusfikurrahaman.learnjapaneseapp  	MobileAds &com.mdmusfikurrahaman.learnjapaneseapp  Modifier &com.mdmusfikurrahaman.learnjapaneseapp  NetworkCapabilities &com.mdmusfikurrahaman.learnjapaneseapp  R &com.mdmusfikurrahaman.learnjapaneseapp  String &com.mdmusfikurrahaman.learnjapaneseapp  SuppressLint &com.mdmusfikurrahaman.learnjapaneseapp  Surface &com.mdmusfikurrahaman.learnjapaneseapp  TextToSpeech &com.mdmusfikurrahaman.learnjapaneseapp  
ToneGenerator &com.mdmusfikurrahaman.learnjapaneseapp  WebChromeClient &com.mdmusfikurrahaman.learnjapaneseapp  WebSettings &com.mdmusfikurrahaman.learnjapaneseapp  
WebViewClient &com.mdmusfikurrahaman.learnjapaneseapp  
WebViewScreen &com.mdmusfikurrahaman.learnjapaneseapp  android &com.mdmusfikurrahaman.learnjapaneseapp  any &com.mdmusfikurrahaman.learnjapaneseapp  apply &com.mdmusfikurrahaman.learnjapaneseapp  code &com.mdmusfikurrahaman.learnjapaneseapp  com &com.mdmusfikurrahaman.learnjapaneseapp  contains &com.mdmusfikurrahaman.learnjapaneseapp  fillMaxSize &com.mdmusfikurrahaman.learnjapaneseapp  fillMaxWidth &com.mdmusfikurrahaman.learnjapaneseapp  filter &com.mdmusfikurrahaman.learnjapaneseapp  find &com.mdmusfikurrahaman.learnjapaneseapp  forEach &com.mdmusfikurrahaman.learnjapaneseapp  isNetworkAvailable &com.mdmusfikurrahaman.learnjapaneseapp  
isNotEmpty &com.mdmusfikurrahaman.learnjapaneseapp  
isOfflineMode &com.mdmusfikurrahaman.learnjapaneseapp  joinToString &com.mdmusfikurrahaman.learnjapaneseapp  let &com.mdmusfikurrahaman.learnjapaneseapp  listOf &com.mdmusfikurrahaman.learnjapaneseapp  map &com.mdmusfikurrahaman.learnjapaneseapp  mapOf &com.mdmusfikurrahaman.learnjapaneseapp  mutableMapOf &com.mdmusfikurrahaman.learnjapaneseapp  playOfflineAudioFeedback &com.mdmusfikurrahaman.learnjapaneseapp  replace &com.mdmusfikurrahaman.learnjapaneseapp  
runOnUiThread &com.mdmusfikurrahaman.learnjapaneseapp  set &com.mdmusfikurrahaman.learnjapaneseapp  
startActivity &com.mdmusfikurrahaman.learnjapaneseapp  to &com.mdmusfikurrahaman.learnjapaneseapp  toString &com.mdmusfikurrahaman.learnjapaneseapp  tts &com.mdmusfikurrahaman.learnjapaneseapp  weight &com.mdmusfikurrahaman.learnjapaneseapp  wrapContentHeight &com.mdmusfikurrahaman.learnjapaneseapp  Any 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  AudioManager 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  BannerAdView 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  Boolean 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  Bundle 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  Column 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  ConnectivityManager 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  Context 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  	Exception 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  Int 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  JavaScriptInterface 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  LearnJapaneseAppTheme 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  Locale 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  Log 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  
MaterialTheme 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  	MobileAds 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  Modifier 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  NetworkCapabilities 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  String 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  SuppressLint 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  Surface 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  TextToSpeech 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  
ToneGenerator 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  
WebViewScreen 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  android 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  any 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  checkAvailableLanguages 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  code 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  configureTTSForOffline 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  contains 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  fillMaxSize 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  fillMaxWidth 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  filter 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  find 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  getSystemService 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  initializeOfflineFallback 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  isNetworkAvailable 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  
isNotEmpty 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  
isOfflineMode 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  joinToString 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  let 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  listOf 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  map 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  mapOf 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  mutableMapOf 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  playOfflineAudioFeedback 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  replace 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  
runOnUiThread 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  set 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  
setContent 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  
startActivity 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  to 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  toString 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  
toneGenerator 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  tts 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  weight 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  wrapContentHeight 3com.mdmusfikurrahaman.learnjapaneseapp.MainActivity  Locale Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  Log Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  TextToSpeech Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  android Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  any Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  breakDownAndSpeakSentence Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  'breakDownAndSpeakSentenceOfflineCapable Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  breakDownByPatterns Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  !breakDownByPatternsOfflineCapable Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  code Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  contains Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  $handleJapaneseSentenceOfflineCapable Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  isNetworkAvailable Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  
isNotEmpty Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  
isOfflineMode Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  joinToString Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  map Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  mapOf Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  mutableMapOf Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  playOfflineAudioFeedback Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  replace Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  
runOnUiThread Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  set Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  speakEnglishTextOfflineCapable Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  speakJapaneseTextOfflineCapable Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  $speakRomanizedJapaneseOfflineCapable Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  
startActivity Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  to Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  toString Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  tts Gcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.JavaScriptInterface  webkit ;com.mdmusfikurrahaman.learnjapaneseapp.MainActivity.android  JavascriptInterface Bcom.mdmusfikurrahaman.learnjapaneseapp.MainActivity.android.webkit  banner_ad_unit_id /com.mdmusfikurrahaman.learnjapaneseapp.R.string  OnInitListener 3com.mdmusfikurrahaman.learnjapaneseapp.TextToSpeech  webkit .com.mdmusfikurrahaman.learnjapaneseapp.android  JavascriptInterface 5com.mdmusfikurrahaman.learnjapaneseapp.android.webkit  Boolean /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  Build /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  
Composable /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  DarkColorScheme /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  
FontFamily /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  
FontWeight /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  LearnJapaneseAppTheme /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  LightColorScheme /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  Pink40 /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  Pink80 /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  Purple40 /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  Purple80 /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  PurpleGrey40 /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  PurpleGrey80 /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  
Typography /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  Unit /com.mdmusfikurrahaman.learnjapaneseapp.ui.theme  	Exception 	java.lang  Runnable 	java.lang  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  Locale 	java.util  ENGLISH java.util.Locale  JAPAN java.util.Locale  JAPANESE java.util.Locale  displayName java.util.Locale  
getDefault java.util.Locale  language java.util.Locale  to java.util.Locale  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  Nothing kotlin  Pair kotlin  Result kotlin  apply kotlin  code kotlin  getCode kotlin  let kotlin  map kotlin  to kotlin  toString kotlin  toString 
kotlin.Any  not kotlin.Boolean  code kotlin.Char  sp 
kotlin.Double  	compareTo 
kotlin.Int  rangeTo 
kotlin.Int  toString 
kotlin.Int  
component1 kotlin.Pair  
component2 kotlin.Pair  any 
kotlin.String  contains 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  replace 
kotlin.String  to 
kotlin.String  message kotlin.Throwable  List kotlin.collections  Map kotlin.collections  
MutableMap kotlin.collections  any kotlin.collections  contains kotlin.collections  filter kotlin.collections  find kotlin.collections  forEach kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  mutableMapOf kotlin.collections  set kotlin.collections  toString kotlin.collections  find kotlin.collections.List  joinToString kotlin.collections.List  size kotlin.collections.List  Entry kotlin.collections.Map  get kotlin.collections.Map  key kotlin.collections.Map.Entry  value kotlin.collections.Map.Entry  map kotlin.collections.MutableMap  set kotlin.collections.MutableMap  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  contains 
kotlin.ranges  contains kotlin.ranges.IntRange  Sequence kotlin.sequences  any kotlin.sequences  contains kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  any kotlin.text  contains kotlin.text  filter kotlin.text  find kotlin.text  forEach kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  replace kotlin.text  set kotlin.text  toString kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            