1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mdmusfikurrahaman.learnjapaneseapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="25"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
12-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:25:5-79
13-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:25:22-76
14    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
14-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:27:5-82
14-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:27:22-79
15    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
15-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:28:5-88
15-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:28:22-85
16    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
16-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:29:5-83
16-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:29:22-80
17    <queries>
17-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:35:5-51:15
18
19        <!-- For browser content -->
20        <intent>
20-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:38:9-44:18
21            <action android:name="android.intent.action.VIEW" />
21-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:39:13-65
21-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:39:21-62
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:41:13-74
23-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:41:23-71
24
25            <data android:scheme="https" />
25-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:43:13-44
25-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:43:19-41
26        </intent>
27        <!-- End of browser content -->
28        <!-- For CustomTabsService -->
29        <intent>
29-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:47:9-49:18
30            <action android:name="android.support.customtabs.action.CustomTabsService" />
30-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:48:13-90
30-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:48:21-87
31        </intent>
32        <!-- End of CustomTabsService -->
33    </queries>
34
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96fd194719fc34b3c2fd3245325ee23\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
35-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d96fd194719fc34b3c2fd3245325ee23\transformed\play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
36-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
36-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
37
38    <permission
38-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
39        android:name="com.mdmusfikurrahaman.learnjapaneseapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.mdmusfikurrahaman.learnjapaneseapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
43
44    <application
44-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:7:5-32:19
45        android:allowBackup="true"
45-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:8:9-35
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a42abb6d6494ce25d6213be6f37263e\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
47        android:dataExtractionRules="@xml/data_extraction_rules"
47-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:10:9-65
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:fullBackupContent="@xml/backup_rules"
50-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:11:9-54
51        android:icon="@mipmap/ic_launcher"
51-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:12:9-43
52        android:label="@string/app_name"
52-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:13:9-41
53        android:roundIcon="@mipmap/ic_launcher_round"
53-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:14:9-54
54        android:supportsRtl="true"
54-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:15:9-35
55        android:testOnly="true"
56        android:theme="@style/Theme.LearnJapaneseApp"
56-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:16:9-54
57        android:usesCleartextTraffic="true" >
57-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:9:9-44
58        <meta-data
58-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:18:9-20:69
59            android:name="com.google.android.gms.ads.APPLICATION_ID"
59-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:19:13-69
60            android:value="ca-app-pub-2281902770675036~1560909931" />
60-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:20:13-67
61
62        <activity
62-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:21:9-31:20
63            android:name="com.mdmusfikurrahaman.learnjapaneseapp.MainActivity"
63-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:22:13-41
64            android:exported="true"
64-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:23:13-36
65            android:label="@string/app_name"
65-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:24:13-45
66            android:theme="@style/Theme.LearnJapaneseApp" >
66-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:25:13-58
67            <intent-filter>
67-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:26:13-30:29
68                <action android:name="android.intent.action.MAIN" />
68-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:27:17-69
68-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:27:25-66
69
70                <category android:name="android.intent.category.LAUNCHER" />
70-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:29:17-77
70-->C:\Users\<USER>\AndroidStudioProjects\LearnJapaneseApp\app\src\main\AndroidManifest.xml:29:27-74
71            </intent-filter>
72        </activity>
73        <activity
73-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
74            android:name="androidx.compose.ui.tooling.PreviewActivity"
74-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
75            android:exported="true" />
75-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
76        <activity
76-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
77            android:name="androidx.activity.ComponentActivity"
77-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
78            android:exported="true" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
78-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
79        <activity
79-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:56:9-61:43
80            android:name="com.google.android.gms.ads.AdActivity"
80-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:57:13-65
81            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
81-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:58:13-122
82            android:exported="false"
82-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:59:13-37
83            android:theme="@android:style/Theme.Translucent" />
83-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:60:13-61
84
85        <provider
85-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:63:9-68:43
86            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
86-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:64:13-76
87            android:authorities="com.mdmusfikurrahaman.learnjapaneseapp.mobileadsinitprovider"
87-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:65:13-73
88            android:exported="false"
88-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:66:13-37
89            android:initOrder="100" />
89-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:67:13-36
90
91        <service
91-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:70:9-74:43
92            android:name="com.google.android.gms.ads.AdService"
92-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:71:13-64
93            android:enabled="true"
93-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:72:13-35
94            android:exported="false" />
94-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:73:13-37
95
96        <activity
96-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:76:9-80:43
97            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
97-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:77:13-82
98            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
98-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:78:13-122
99            android:exported="false" />
99-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:79:13-37
100        <activity
100-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:81:9-88:43
101            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
101-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:82:13-82
102            android:excludeFromRecents="true"
102-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:83:13-46
103            android:exported="false"
103-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:84:13-37
104            android:launchMode="singleTask"
104-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:85:13-44
105            android:taskAffinity=""
105-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:86:13-36
106            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
106-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:87:13-72
107
108        <property
108-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:90:9-92:62
109            android:name="android.adservices.AD_SERVICES_CONFIG"
109-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:91:13-65
110            android:resource="@xml/gma_ad_services_config" />
110-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f2b8184790540c614f31165c055d110\transformed\play-services-ads-lite-23.0.0\AndroidManifest.xml:92:13-59
111
112        <activity
112-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
113            android:name="com.google.android.gms.common.api.GoogleApiActivity"
113-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\AndroidManifest.xml:20:19-85
114            android:exported="false"
114-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\AndroidManifest.xml:22:19-43
115            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
115-->[com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2ea4acbe4700842098c83253cdfc736\transformed\play-services-base-18.0.0\AndroidManifest.xml:21:19-78
116
117        <meta-data
117-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3448ec15e9e04b037c5166989b571054\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
118            android:name="com.google.android.gms.version"
118-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3448ec15e9e04b037c5166989b571054\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
119            android:value="@integer/google_play_services_version" />
119-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3448ec15e9e04b037c5166989b571054\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
120
121        <provider
121-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
122            android:name="androidx.startup.InitializationProvider"
122-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
123            android:authorities="com.mdmusfikurrahaman.learnjapaneseapp.androidx-startup"
123-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
124            android:exported="false" >
124-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
125            <meta-data
125-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
126                android:name="androidx.work.WorkManagerInitializer"
126-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
127                android:value="androidx.startup" />
127-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
128            <meta-data
128-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
129                android:name="androidx.emoji2.text.EmojiCompatInitializer"
129-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
130                android:value="androidx.startup" />
130-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
131            <meta-data
131-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\845fee041083134cc6ba01390c516581\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
132                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
132-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\845fee041083134cc6ba01390c516581\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
133                android:value="androidx.startup" />
133-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\845fee041083134cc6ba01390c516581\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
134            <meta-data
134-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
135                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
135-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
136                android:value="androidx.startup" />
136-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
137        </provider>
138
139        <service
139-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
140            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
140-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
141            android:directBootAware="false"
141-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
142            android:enabled="@bool/enable_system_alarm_service_default"
142-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
143            android:exported="false" />
143-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
144        <service
144-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
145            android:name="androidx.work.impl.background.systemjob.SystemJobService"
145-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
146            android:directBootAware="false"
146-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
147            android:enabled="@bool/enable_system_job_service_default"
147-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
148            android:exported="true"
148-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
149            android:permission="android.permission.BIND_JOB_SERVICE" />
149-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
150        <service
150-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
151            android:name="androidx.work.impl.foreground.SystemForegroundService"
151-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
152            android:directBootAware="false"
152-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
153            android:enabled="@bool/enable_system_foreground_service_default"
153-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
154            android:exported="false" />
154-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
155
156        <receiver
156-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
157            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
157-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
158            android:directBootAware="false"
158-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
159            android:enabled="true"
159-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
160            android:exported="false" />
160-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
161        <receiver
161-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
162            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
162-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
163            android:directBootAware="false"
163-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
164            android:enabled="false"
164-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
165            android:exported="false" >
165-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
166            <intent-filter>
166-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
167                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
167-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
167-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
168                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
168-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
168-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
169            </intent-filter>
170        </receiver>
171        <receiver
171-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
172            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
172-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
173            android:directBootAware="false"
173-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
174            android:enabled="false"
174-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
175            android:exported="false" >
175-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
176            <intent-filter>
176-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
177                <action android:name="android.intent.action.BATTERY_OKAY" />
177-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
177-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
178                <action android:name="android.intent.action.BATTERY_LOW" />
178-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
178-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
179            </intent-filter>
180        </receiver>
181        <receiver
181-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
182            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
182-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
183            android:directBootAware="false"
183-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
184            android:enabled="false"
184-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
185            android:exported="false" >
185-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
186            <intent-filter>
186-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
187                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
187-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
187-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
188                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
188-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
188-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
189            </intent-filter>
190        </receiver>
191        <receiver
191-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
192            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
192-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
193            android:directBootAware="false"
193-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
194            android:enabled="false"
194-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
195            android:exported="false" >
195-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
196            <intent-filter>
196-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
197                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
197-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
197-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
198            </intent-filter>
199        </receiver>
200        <receiver
200-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
201            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
201-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
202            android:directBootAware="false"
202-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
203            android:enabled="false"
203-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
204            android:exported="false" >
204-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
205            <intent-filter>
205-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
206                <action android:name="android.intent.action.BOOT_COMPLETED" />
206-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
206-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
207                <action android:name="android.intent.action.TIME_SET" />
207-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
207-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
208                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
208-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
208-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
209            </intent-filter>
210        </receiver>
211        <receiver
211-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
212            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
212-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
213            android:directBootAware="false"
213-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
214            android:enabled="@bool/enable_system_alarm_service_default"
214-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
215            android:exported="false" >
215-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
216            <intent-filter>
216-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
217                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
217-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
217-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
218            </intent-filter>
219        </receiver>
220        <receiver
220-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
221            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
221-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
222            android:directBootAware="false"
222-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
223            android:enabled="true"
223-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
224            android:exported="true"
224-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
225            android:permission="android.permission.DUMP" >
225-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
226            <intent-filter>
226-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
227                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
227-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e612104df3321f05aa271207b528de3\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
228            </intent-filter>
229        </receiver>
230
231        <uses-library
231-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97fe37fc9d0c2fe8e7f7c6b93f8353d6\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
232            android:name="android.ext.adservices"
232-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97fe37fc9d0c2fe8e7f7c6b93f8353d6\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
233            android:required="false" />
233-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97fe37fc9d0c2fe8e7f7c6b93f8353d6\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
234
235        <receiver
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
236            android:name="androidx.profileinstaller.ProfileInstallReceiver"
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
237            android:directBootAware="false"
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
238            android:enabled="true"
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
239            android:exported="true"
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
240            android:permission="android.permission.DUMP" >
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
241            <intent-filter>
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
242                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
243            </intent-filter>
244            <intent-filter>
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
245                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
246            </intent-filter>
247            <intent-filter>
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
248                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
249            </intent-filter>
250            <intent-filter>
250-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
251                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
251-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
251-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edd5fe54ef83bf86f933c89c20f33cd9\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
252            </intent-filter>
253        </receiver>
254
255        <service
255-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
256            android:name="androidx.room.MultiInstanceInvalidationService"
256-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
257            android:directBootAware="true"
257-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
258            android:exported="false" />
258-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e06cc8a788b719a1c9dd60b1f6e321b2\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
259    </application>
260
261</manifest>
